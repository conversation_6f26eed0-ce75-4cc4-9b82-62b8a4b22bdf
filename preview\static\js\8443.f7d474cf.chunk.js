"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[8443],{38443:function(e,s,a){a.r(s),a.d(s,{Typography:function(){return n}});var i=a(15671),c=a(43144),d=a(60136),l=a(54062),r=a(72791),t=a(80184),n=function(e){(0,d.Z)(a,e);var s=(0,l.Z)(a);function a(){return(0,i.Z)(this,a),s.apply(this,arguments)}return(0,c.Z)(a,[{key:"render",value:function(){return(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"row",children:[(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Headings"}),(0,t.jsxs)("p",{className:"card-description",children:[" Add tags ",(0,t.jsx)("code",{children:"<h1>"})," to ",(0,t.jsx)("code",{children:"<h6>"})," or class ",(0,t.jsx)("code",{children:".h1"})," to ",(0,t.jsx)("code",{children:".h6"})]}),(0,t.jsxs)("div",{className:"template-demo",children:[(0,t.jsx)("h1",{children:"h1. Heading"}),(0,t.jsx)("h2",{children:"h2. Heading"}),(0,t.jsx)("h3",{children:"h3. Heading"}),(0,t.jsx)("h4",{children:"h4. Heading"}),(0,t.jsx)("h5",{children:"h5. Heading"}),(0,t.jsx)("h6",{children:"h6. Heading"})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Headings with secondary text"}),(0,t.jsx)("p",{className:"card-description",children:" Add faded secondary text to headings "}),(0,t.jsxs)("div",{className:"template-demo",children:[(0,t.jsxs)("h1",{children:[" h1. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]}),(0,t.jsxs)("h2",{children:[" h2. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]}),(0,t.jsxs)("h3",{children:[" h3. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]}),(0,t.jsxs)("h4",{children:[" h4. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]}),(0,t.jsxs)("h5",{children:[" h5. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]}),(0,t.jsxs)("h6",{children:[" h6. Heading ",(0,t.jsx)("small",{className:"text-muted",children:" Secondary text "})]})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Display headings"}),(0,t.jsxs)("p",{className:"card-description",children:[" Add class ",(0,t.jsx)("code",{children:".display1"})," to ",(0,t.jsx)("code",{children:".display-4"})]}),(0,t.jsxs)("div",{className:"template-demo",children:[(0,t.jsx)("h1",{className:"display-1",children:"Display 1"}),(0,t.jsx)("h1",{className:"display-2",children:"Display 2"}),(0,t.jsx)("h1",{className:"display-3",children:"Display 3"}),(0,t.jsx)("h1",{className:"display-4",children:"Display 4"})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 d-flex align-items-stretch",children:(0,t.jsxs)("div",{className:"row",children:[(0,t.jsx)("div",{className:"col-md-12 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Paragraph"}),(0,t.jsxs)("p",{className:"card-description",children:[" Write text in ",(0,t.jsx)("code",{children:"<p>"})," tag "]}),(0,t.jsx)("p",{children:" Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley not only five centuries, "})]})})}),(0,t.jsx)("div",{className:"col-md-12 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Icon size"}),(0,t.jsxs)("p",{className:"card-description",children:[" Add class ",(0,t.jsx)("code",{children:".icon-lg"}),", ",(0,t.jsx)("code",{children:".icon-md"}),", ",(0,t.jsx)("code",{children:".icon-sm"})]}),(0,t.jsxs)("div",{className:"row",children:[(0,t.jsx)("div",{className:"col-md-4 d-flex align-items-center",children:(0,t.jsxs)("div",{className:"d-flex flex-row align-items-center",children:[(0,t.jsx)("i",{className:"ti-package icon-lg text-warning"}),(0,t.jsx)("p",{className:"mb-0 ms-1",children:" Icon-lg "})]})}),(0,t.jsx)("div",{className:"col-md-4 d-flex align-items-center",children:(0,t.jsxs)("div",{className:"d-flex flex-row align-items-center",children:[(0,t.jsx)("i",{className:"ti-package icon-md text-success"}),(0,t.jsx)("p",{className:"mb-0 ms-1",children:" Icon-md "})]})}),(0,t.jsx)("div",{className:"col-md-4 d-flex align-items-center",children:(0,t.jsxs)("div",{className:"d-flex flex-row align-items-center",children:[(0,t.jsx)("i",{className:"ti-package icon-sm text-danger"}),(0,t.jsx)("p",{className:"mb-0 ms-1",children:" Icon-sm "})]})})]})]})})})]})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Blockquotes"}),(0,t.jsxs)("p",{className:"card-description",children:[" Wrap content inside",(0,t.jsx)("code",{children:'<blockquote className="blockquote">'})]}),(0,t.jsx)("blockquote",{className:"blockquote",children:(0,t.jsx)("p",{className:"mb-0",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante."})})]}),(0,t.jsx)("div",{className:"card-body",children:(0,t.jsxs)("blockquote",{className:"blockquote blockquote-primary",children:[(0,t.jsx)("p",{children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante."}),(0,t.jsxs)("footer",{className:"blockquote-footer text-muted",children:["Someone famous in ",(0,t.jsx)("cite",{title:"Source Title",children:"Source Title"})]})]})})]})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Address"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use ",(0,t.jsx)("code",{children:"<address>"})," tag "]}),(0,t.jsxs)("div",{className:"row",children:[(0,t.jsx)("div",{className:"col-md-6",children:(0,t.jsxs)("address",{children:[(0,t.jsx)("p",{className:"font-weight-bold",children:"staradmin imc"}),(0,t.jsx)("p",{children:" 695 lsom Ave, "}),(0,t.jsx)("p",{children:" Suite 00 "}),(0,t.jsx)("p",{children:" San Francisco, CA 94107 "})]})}),(0,t.jsx)("div",{className:"col-md-6",children:(0,t.jsxs)("address",{className:"text-primary",children:[(0,t.jsx)("p",{className:"font-weight-bold",children:" E-mail "}),(0,t.jsx)("p",{className:"mb-2",children:" <EMAIL> "}),(0,t.jsx)("p",{className:"font-weight-bold",children:" Web Address "}),(0,t.jsx)("p",{children:" www.staradmin.com "})]})})]})]}),(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Lead"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".lead"})]}),(0,t.jsx)("p",{className:"lead",children:" Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. "})]})]})}),(0,t.jsx)("div",{className:"col-md-12 grid-margin",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Text colors"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".text-primary"}),", ",(0,t.jsx)("code",{children:".text-secondary"})," etc. for text in theme colors "]}),(0,t.jsxs)("div",{className:"row",children:[(0,t.jsxs)("div",{className:"col-md-6",children:[(0,t.jsx)("p",{className:"text-primary",children:".text-primary"}),(0,t.jsx)("p",{className:"text-success",children:".text-success"}),(0,t.jsx)("p",{className:"text-danger",children:".text-danger"}),(0,t.jsx)("p",{className:"text-warning",children:".text-warning"}),(0,t.jsx)("p",{className:"text-info",children:".text-info"})]}),(0,t.jsxs)("div",{className:"col-md-6",children:[(0,t.jsx)("p",{className:"text-light bg-dark pl-1",children:".text-light"}),(0,t.jsx)("p",{className:"text-secondary",children:".text-secondary"}),(0,t.jsx)("p",{className:"text-dark",children:".text-dark"}),(0,t.jsx)("p",{className:"text-muted",children:".text-muted"}),(0,t.jsx)("p",{className:"text-white bg-dark pl-1",children:".text-white"})]})]})]})})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Top aligned media"}),(0,t.jsxs)("div",{className:"media",children:[(0,t.jsx)("i",{className:"ti-world icon-md text-info d-flex align-self-start me-3"}),(0,t.jsx)("div",{className:"media-body",children:(0,t.jsx)("p",{className:"card-text",children:"Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque."})})]})]})})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Center aligned media"}),(0,t.jsxs)("div",{className:"media",children:[(0,t.jsx)("i",{className:"ti-world icon-md text-info d-flex align-self-center me-3"}),(0,t.jsx)("div",{className:"media-body",children:(0,t.jsx)("p",{className:"card-text",children:"Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque."})})]})]})})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Bottom aligned media"}),(0,t.jsxs)("div",{className:"media",children:[(0,t.jsx)("i",{className:"ti-world icon-md text-info d-flex align-self-end me-3"}),(0,t.jsx)("div",{className:"media-body",children:(0,t.jsx)("p",{className:"card-text",children:"Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque."})})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Highlighted Text"}),(0,t.jsxs)("p",{className:"card-description",children:[" Wrap the text in ",(0,t.jsx)("code",{children:"<mark>"})," to highlight text "]}),(0,t.jsxs)("p",{children:[" It is a long ",(0,t.jsx)("mark",{className:"bg-warning text-white",children:"established"})," fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution "]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"List Unordered"}),(0,t.jsxs)("ul",{children:[(0,t.jsx)("li",{children:"Lorem ipsum dolor sit amet"}),(0,t.jsx)("li",{children:"Consectetur adipiscing elit"}),(0,t.jsx)("li",{children:"Integer molestie lorem at massa"}),(0,t.jsx)("li",{children:"Facilisis in pretium nisl aliquet"}),(0,t.jsx)("li",{children:"Nulla volutpat aliquam velit"})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Bold text"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class",(0,t.jsx)("code",{children:".font-weight-bold"})]}),(0,t.jsxs)("p",{children:[" It is a long ",(0,t.jsx)("span",{className:"font-weight-bold",children:"established fact"})," that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution "]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"List Ordered"}),(0,t.jsxs)("ol",{children:[(0,t.jsx)("li",{children:"Lorem ipsum dolor sit amet"}),(0,t.jsx)("li",{children:"Consectetur adipiscing elit"}),(0,t.jsx)("li",{children:"Integer molestie lorem at massa"}),(0,t.jsx)("li",{children:"Facilisis in pretium nisl aliquet"}),(0,t.jsx)("li",{children:"Nulla volutpat aliquam velit>"})]})]})})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title text-primary",children:"Underline"}),(0,t.jsxs)("p",{className:"card-description",children:[" Wrap in ",(0,t.jsx)("code",{children:"<u>"})," tag for underline "]}),(0,t.jsx)("p",{children:(0,t.jsx)("u",{children:"lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua."})})]}),(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title text-danger",children:"Lowercase"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".text-lowercase"})]}),(0,t.jsx)("p",{className:"text-lowercase",children:" lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. "})]}),(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title text-warning",children:"Uppercase"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".text-uppercase"})]}),(0,t.jsx)("p",{className:"text-uppercase",children:" lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. "})]})]})}),(0,t.jsx)("div",{className:"col-md-6 grid-margin stretch-card",children:(0,t.jsxs)("div",{className:"card",children:[(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"Mute"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".text-muted"})]}),(0,t.jsx)("p",{className:"text-muted",children:" lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. "})]}),(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title text-success",children:"Strike"}),(0,t.jsxs)("p",{className:"card-description",children:[" Wrap content in ",(0,t.jsx)("code",{children:"<del>"})," tag "]}),(0,t.jsx)("p",{children:(0,t.jsx)("del",{children:" lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. "})})]}),(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title text-info",children:"Capitalized"}),(0,t.jsxs)("p",{className:"card-description",children:[" Use class ",(0,t.jsx)("code",{children:".text-capitalize"})]}),(0,t.jsx)("p",{className:"text-capitalize",children:" lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. "})]})]})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"List with icon"}),(0,t.jsxs)("p",{className:"card-description",children:["Add class ",(0,t.jsx)("code",{children:".list-ticked"})," to ",(0,t.jsx)("code",{children:"<ul>"})]}),(0,t.jsxs)("ul",{className:"list-ticked",children:[(0,t.jsx)("li",{children:"Lorem ipsum dolor sit amet"}),(0,t.jsx)("li",{children:"Consectetur adipiscing elit"}),(0,t.jsx)("li",{children:"Integer molestie lorem at massa"}),(0,t.jsx)("li",{children:"Facilisis in pretium nisl aliquet"}),(0,t.jsx)("li",{children:"Nulla volutpat aliquam velit>"})]})]})})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"List with icon"}),(0,t.jsxs)("p",{className:"card-description",children:["Add class ",(0,t.jsx)("code",{children:".list-arrow"})," to ",(0,t.jsx)("code",{children:"<ul>"})]}),(0,t.jsxs)("ul",{className:"list-arrow",children:[(0,t.jsx)("li",{children:"Lorem ipsum dolor sit amet"}),(0,t.jsx)("li",{children:"Consectetur adipiscing elit"}),(0,t.jsx)("li",{children:"Integer molestie lorem at massa"}),(0,t.jsx)("li",{children:"Facilisis in pretium nisl aliquet"}),(0,t.jsx)("li",{children:"Nulla volutpat aliquam velit>"})]})]})})}),(0,t.jsx)("div",{className:"col-md-4 grid-margin stretch-card",children:(0,t.jsx)("div",{className:"card",children:(0,t.jsxs)("div",{className:"card-body",children:[(0,t.jsx)("h4",{className:"card-title",children:"List with icon"}),(0,t.jsxs)("p",{className:"card-description",children:["Add class ",(0,t.jsx)("code",{children:".list-star"})," to ",(0,t.jsx)("code",{children:"<ul>"})]}),(0,t.jsxs)("ul",{className:"list-star",children:[(0,t.jsx)("li",{children:"Lorem ipsum dolor sit amet"}),(0,t.jsx)("li",{children:"Consectetur adipiscing elit"}),(0,t.jsx)("li",{children:"Integer molestie lorem at massa"}),(0,t.jsx)("li",{children:"Facilisis in pretium nisl aliquet"}),(0,t.jsx)("li",{children:"Nulla volutpat aliquam velit>"})]})]})})})]})})}}]),a}(r.Component);s.default=n}}]);
//# sourceMappingURL=8443.f7d474cf.chunk.js.map