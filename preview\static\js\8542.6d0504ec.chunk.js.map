{"version": 3, "file": "static/js/8542.6d0504ec.chunk.js", "mappings": "8cAmDA,SAASA,EAAcC,EAAKC,EAAKC,GAC/B,IAAMC,GAAcH,EAAMC,IAAQC,EAAMD,GAAO,IAC/C,OAAOG,KAAKC,MA/CU,IA+CJF,GA/CI,GAgDvB,CAED,SAASG,EAAT,EAaGC,GAAK,MAZNN,EAYM,EAZNA,IACAD,EAWM,EAXNA,IACAE,EAUM,EAVNA,IACAM,EASM,EATNA,MACAC,EAQM,EARNA,eACAC,EAOM,EAPNA,QACAC,EAMM,EANNA,SACAC,EAKM,EALNA,UACAC,EAIM,EAJNA,MACAC,EAGM,EAHNA,QACAC,EAEM,EAFNA,SACGC,GACG,YACN,OAAoBC,EAAAA,EAAAA,KAAK,OAAD,QAAC,QACvBV,IAAKA,GACFS,GAFmB,IAGtBE,KAAM,cACNN,UAAWO,IAAWP,EAAD,UAAeG,EAAf,qCACZD,GAAYA,IADA,mBAEfC,EAFe,iBAEWJ,IAFX,mBAGfI,EAHe,gBAGUJ,GAAYD,GAHtB,IAKrBG,OAAO,QACLO,MAAO,GAAF,OAAKrB,EAAcC,EAAKC,EAAKC,GAA7B,MACFW,GAEL,gBAAiBb,EACjB,gBAAiBC,EACjB,gBAAiBC,EACjBmB,SAAUZ,GAA8BQ,EAAAA,EAAAA,KAAK,OAAQ,CACnDL,UAAW,kBACXS,SAAUb,IACPA,IAER,CAED,IAAMc,EAA2BC,EAAAA,YAAiB,WAG/ChB,GAAQ,IAFTiB,EAES,EAFTA,QACGR,GACM,YAGT,GAFAA,EAAMD,UAAWU,EAAAA,EAAAA,IAAmBT,EAAMD,SAAU,YAEhDS,EACF,OAAOlB,EAAkBU,EAAOT,GAGlC,IACEN,EAYEe,EAZFf,IACAD,EAWEgB,EAXFhB,IACAE,EAUEc,EAVFd,IACAM,EASEQ,EATFR,MACAC,EAQEO,EARFP,eACAC,EAOEM,EAPFN,QACAC,EAMEK,EANFL,SACAI,EAKEC,EALFD,SACAD,EAIEE,EAJFF,QACAF,EAGEI,EAHFJ,UACAS,EAEEL,EAFFK,SACGK,GAZL,OAaIV,EAbJ,GAcA,OAAoBC,EAAAA,EAAAA,KAAK,OAAD,QAAC,QACvBV,IAAKA,GACFmB,GAFmB,IAGtBd,UAAWO,IAAWP,EAAWG,GACjCM,SAAUA,GAAWM,EAAAA,EAAAA,IAAIN,GAAU,SAAAO,GAAK,OAAiBC,EAAAA,EAAAA,cAAaD,EAAO,CAC3EJ,SAAS,GAD6B,IAElClB,EAAkB,CACtBL,IAAAA,EACAD,IAAAA,EACAE,IAAAA,EACAM,MAAAA,EACAC,eAAAA,EACAC,QAAAA,EACAC,SAAAA,EACAI,SAAAA,EACAD,QAAAA,GACCP,KAEN,IACDe,EAAYQ,YAAc,cAC1BR,EAAYS,aA/FS,CACnB9B,IAAK,EACLC,IAAK,IACLS,UAAU,EACVa,SAAS,EACTf,gBAAgB,EAChBC,SAAS,GA0FX,K,uCCxIA,SAASsB,EAAQC,GAWf,OATED,EADoB,oBAAXE,QAAoD,kBAApBA,OAAOC,SACtC,SAAUF,GAClB,cAAcA,CACf,EAES,SAAUA,GAClB,OAAOA,GAAyB,oBAAXC,QAAyBD,EAAIG,cAAgBF,QAAUD,IAAQC,OAAOG,UAAY,gBAAkBJ,CAC1H,EAGID,EAAQC,EAChB,CAED,SAASK,EAAgBC,EAAUC,GACjC,KAAMD,aAAoBC,GACxB,MAAM,IAAIC,UAAU,oCAEvB,CAED,SAASC,EAAkBC,EAAQ3B,GACjC,IAAK,IAAI4B,EAAI,EAAGA,EAAI5B,EAAM6B,OAAQD,IAAK,CACrC,IAAIE,EAAa9B,EAAM4B,GACvBE,EAAWC,WAAaD,EAAWC,aAAc,EACjDD,EAAWE,cAAe,EACtB,UAAWF,IAAYA,EAAWG,UAAW,GACjDC,OAAOC,eAAeR,EAAQG,EAAWM,IAAKN,EAC/C,CACF,CAED,SAASO,EAAab,EAAac,EAAYC,GAG7C,OAFID,GAAYZ,EAAkBF,EAAYH,UAAWiB,GACrDC,GAAab,EAAkBF,EAAae,GACzCf,CACR,CAED,SAASgB,EAAgBvB,EAAKmB,EAAKK,GAYjC,OAXIL,KAAOnB,EACTiB,OAAOC,eAAelB,EAAKmB,EAAK,CAC9BK,MAAOA,EACPV,YAAY,EACZC,cAAc,EACdC,UAAU,IAGZhB,EAAImB,GAAOK,EAGNxB,CACR,CAED,SAASyB,IAeP,OAdAA,EAAWR,OAAOS,QAAU,SAAUhB,GACpC,IAAK,IAAIC,EAAI,EAAGA,EAAIgB,UAAUf,OAAQD,IAAK,CACzC,IAAIiB,EAASD,UAAUhB,GAEvB,IAAK,IAAIQ,KAAOS,EACVX,OAAOb,UAAUyB,eAAeC,KAAKF,EAAQT,KAC/CT,EAAOS,GAAOS,EAAOT,GAG1B,CAED,OAAOT,CACR,EAEMe,EAASM,MAAMC,KAAML,UAC7B,CAED,SAASM,EAAcvB,GACrB,IAAK,IAAIC,EAAI,EAAGA,EAAIgB,UAAUf,OAAQD,IAAK,CACzC,IAAIiB,EAAyB,MAAhBD,UAAUhB,GAAagB,UAAUhB,GAAK,CAAC,EAChDuB,EAAUjB,OAAOkB,KAAKP,GAEkB,oBAAjCX,OAAOmB,wBAChBF,EAAUA,EAAQG,OAAOpB,OAAOmB,sBAAsBR,GAAQU,QAAO,SAAUC,GAC7E,OAAOtB,OAAOuB,yBAAyBZ,EAAQW,GAAKzB,UACrD,MAGHoB,EAAQO,SAAQ,SAAUtB,GACxBI,EAAgBb,EAAQS,EAAKS,EAAOT,GACrC,GACF,CAED,OAAOT,CACR,CAED,SAASgC,EAAUC,EAAUC,GAC3B,GAA0B,oBAAfA,GAA4C,OAAfA,EACtC,MAAM,IAAIpC,UAAU,sDAGtBmC,EAASvC,UAAYa,OAAO4B,OAAOD,GAAcA,EAAWxC,UAAW,CACrED,YAAa,CACXqB,MAAOmB,EACP3B,UAAU,EACVD,cAAc,KAGd6B,GAAYE,EAAgBH,EAAUC,EAC3C,CAED,SAASG,EAAgBC,GAIvB,OAHAD,EAAkB9B,OAAOgC,eAAiBhC,OAAOiC,eAAiB,SAAyBF,GACzF,OAAOA,EAAEG,WAAalC,OAAOiC,eAAeF,EAC7C,EACMD,EAAgBC,EACxB,CAED,SAASF,EAAgBE,EAAGI,GAM1B,OALAN,EAAkB7B,OAAOgC,gBAAkB,SAAyBD,EAAGI,GAErE,OADAJ,EAAEG,UAAYC,EACPJ,CACR,EAEMF,EAAgBE,EAAGI,EAC3B,CAED,SAASC,EAAuBC,GAC9B,QAAa,IAATA,EACF,MAAM,IAAIC,eAAe,6DAG3B,OAAOD,CACR,CAED,SAASE,EAA2BF,EAAMxB,GACxC,OAAIA,GAAyB,kBAATA,GAAqC,oBAATA,EAIzCuB,EAAuBC,GAHrBxB,CAIV,CAED,IAAIlD,EAAQ,CACV6E,QAAS,eACTC,aAAc,MACdC,OAAQ,mBACRxE,MAAO,GACPyE,OAAQ,IAENC,EAAQ,CACVC,MAAO7B,EAAc,CAAC,EAAGrD,EAAO,CAC9BmF,gBAAiB,SAEnBC,KAAM/B,EAAc,CAAC,EAAGrD,EAAO,CAC7BmF,gBAAiB,UAEnBE,YAAahC,EAAc,CAAC,EAAGrD,EAAO,CACpCmF,gBAAiB,SAKjBG,EAAY,SAAmBC,GAEjC,OAAI7E,EAAAA,eAAqB6E,GAChBA,EAIa,WAAlBpE,EAAQoE,IAA+B,OAATA,EACzB7E,EAAAA,cAAoB,OAAQ,CACjCV,MAAOuF,IAKkC,oBAAzClD,OAAOb,UAAUgE,SAAStC,KAAKqC,GAC1B7E,EAAAA,cAAoB,OAAQ,CACjCX,UAAWwF,SAFf,CAKD,EAEGE,EAEJ,SAAUC,GAGR,SAASD,IAGP,OAFAhE,EAAgB2B,KAAMqC,GAEfb,EAA2BxB,KAAMe,EAAgBsB,GAActC,MAAMC,KAAML,WACnF,CA4DD,OAlEAe,EAAU2B,EAAcC,GAQxBlD,EAAaiD,EAAc,CAAC,CAC1BlD,IAAK,SACLK,MAAO,WACL,IAAI+C,EAEAC,EAAcxC,KAAKjD,MACnB0F,EAAQD,EAAYC,MACpBC,EAAeF,EAAYE,aAC3BC,EAAaH,EAAYG,WACzBC,EAAUJ,EAAYI,QACtBC,EAAYL,EAAYK,UACxBC,EAAWN,EAAYM,SACvBC,EAAUP,EAAYO,QACtBC,EAAcR,EAAYQ,YAE1BC,EAAiBf,EAAUQ,GAE3BQ,EAAWhB,EAAUS,GAErBQ,GAKD5D,EALuBgD,EAAsB,CAC9Cd,QAAS,eACT2B,SAAU,WACVC,SAAU,SACVC,IAAK,GAC+C,QAAdT,EAAsB,QAAU,OAAQ,GAAItD,EAAgBgD,EAAqB,QAAS,GAAGlC,OAAOuC,EAAS,MAAOL,GACxJ3F,EAAQ,CACV2G,OAAST,EAAuB,UAAZ,UACpBrB,QAAS,eACT2B,SAAU,YAGZ,SAASI,EAAgBC,GACnBT,GACFA,EAAYP,EAAOgB,EAEtB,CAED,SAASC,EAAiBD,GACpBV,IAGFU,EAAEE,iBACFZ,EAAQN,EAAOgB,GAElB,CAED,OAAOnG,EAAAA,cAAoB,OAAQ,CACjCV,MAAOA,EACPmG,QAASW,EACTV,YAAaQ,EACbI,YAAaJ,EACbK,WAAYH,GACXT,EAAgB3F,EAAAA,cAAoB,OAAQ,CAC7CV,MAAOuG,GACND,GACJ,KAGIb,CACR,CApED,CAoEE/E,EAAAA,eAEEwG,EAEJ,SAAUxB,GAGR,SAASwB,EAAO/G,GACd,IAAIgH,EAeJ,OAbA1F,EAAgB2B,KAAM8D,IAEtBC,EAAQvC,EAA2BxB,KAAMe,EAAgB+C,GAAQhE,KAAKE,KAAMjD,KACtEiH,MAAQ,CAGZC,aAAcF,EAAMhH,MAAMyC,MAE1B0E,aAAa,GAEfH,EAAMI,aAAeJ,EAAMI,aAAaC,KAAK/C,EAAuBA,EAAuB0C,KAC3FA,EAAMM,gBAAkBN,EAAMM,gBAAgBD,KAAK/C,EAAuBA,EAAuB0C,KACjGA,EAAMO,YAAcP,EAAMO,YAAYF,KAAK/C,EAAuBA,EAAuB0C,KAClFA,CACR,CA6JD,OAhLArD,EAAUoD,EAAQxB,GAqBlBlD,EAAa0E,EAAQ,CAAC,CACpB3E,IAAK,4BACLK,MAAO,SAAmC+E,GACxC,IAAIC,EAAexE,KAAKjD,MAAMyC,QAAU+E,EAAU/E,MAClDQ,KAAKyE,UAAS,SAAUC,GACtB,MAAO,CACLT,aAAcO,EAAeD,EAAU/E,MAAQkF,EAAUT,aAE5D,GACF,GACA,CACD9E,IAAK,qBACLK,MAAO,SAA4BmF,EAAWD,GAG5C,GAAIC,EAAUnF,QAAUQ,KAAKjD,MAAMyC,MAKnC,OAAIkF,EAAUR,cAAgBlE,KAAKgE,MAAME,YAChClE,KAAKjD,MAAM6H,eAIhB5E,KAAKgE,MAAME,aACblE,KAAKjD,MAAM6H,QAAQ5E,KAAKgE,MAAMC,cAEjC,GACA,CACD9E,IAAK,cACLK,MAAO,SAAqBqF,EAAaC,GACvC,IAAItF,EAAQQ,KAAK+E,sBAAsBF,EAAaC,GACpD9E,KAAKjD,MAAMgG,QAAQvD,EAAOsF,EAC3B,GACA,CACD3F,IAAK,kBACLK,MAAO,SAAyBqF,EAAaC,GAC3C,IAAItF,EAAQQ,KAAK+E,sBAAsBF,EAAaC,GAIpD9E,KAAKyE,SAAS,CACZP,aAAclE,KAAKjD,MAAM+F,SACzBmB,aAAczE,GAEjB,GACA,CACDL,IAAK,eACLK,MAAO,WACLQ,KAAKyE,SAAS,CACZR,aAAcjE,KAAKjD,MAAMyC,MACzB0E,aAAa,GAEhB,GACA,CACD/E,IAAK,wBACLK,MAAO,SAA+BqF,EAAaC,GACjD,IAAI5I,EAAa8D,KAAKgF,yBAAyBF,GAE3CG,EAAW9I,KAAK+I,KAAKhJ,EAAa,EAAI8D,KAAKjD,MAAMoI,WAAanF,KAAKjD,MAAMoI,UAEzEC,EAAYjJ,KAAKkJ,IAAI,GAAI,GACzBpB,EAAeY,GAAe1I,KAAKmJ,MAAMpJ,GAAcC,KAAKmJ,MAAML,EAAWG,GAAaA,GAE9F,OAAOnB,EAAe,EAAIA,EAAejE,KAAKjD,MAAMwI,aAAevF,KAAKjD,MAAMwI,aAAetB,EAAe,EAAIjE,KAAKjD,MAAMoI,SAC5H,GACA,CACDhG,IAAK,2BACLK,MAAO,SAAkCsF,GACvC,IAAIU,EAAUV,EAAMW,YAAYC,KAAKC,QAAQ,UAAY,EAAIb,EAAMW,YAAYC,KAAKC,QAAQ,aAAe,EAAIb,EAAMc,eAAe,GAAGJ,QAAUV,EAAMe,QAAQ,GAAGL,QAAUV,EAAMU,QAC9KM,EAAahB,EAAMpG,OAAOqH,wBAC1BC,EAAiC,QAAzBhG,KAAKjD,MAAM8F,UAAsBiD,EAAWG,MAAQT,EAAUA,EAAUM,EAAWI,KAE/F,OAAOF,EAAQ,EAAI,EAAIA,EAAQF,EAAW3I,KAC3C,GACA,CACDgC,IAAK,SACLK,MAAO,WACL,IAuBI2G,EAvBA3D,EAAcxC,KAAKjD,MACnB+F,EAAWN,EAAYM,SACvBsD,EAAQ5D,EAAY4D,MACpBb,EAAe/C,EAAY+C,aAC3B/F,EAAQgD,EAAYhD,MACpB6G,EAAmB7D,EAAY6D,iBAC/BxD,EAAYL,EAAYK,UACxByD,EAAc9D,EAAY8D,YAC1BC,EAAa/D,EAAY+D,WACzBC,EAAoBhE,EAAYgE,kBAChC7J,EAAY6F,EAAY7F,UACxB8J,EAAKjE,EAAYiE,GACjB7J,EAAQ4F,EAAY5F,MACpB8J,EAAWlE,EAAYkE,SACvBC,EAAc3G,KAAKgE,MACnBC,EAAe0C,EAAY1C,aAC3BC,EAAcyC,EAAYzC,YAC1B0C,EAAc,GACd9E,EAAQ,GAAGzB,OAAOiG,GAClBtE,EAAO,GAAG3B,OAAOkG,GACjBtE,EAAc,GAAG5B,OAAOmG,GACxBK,EAAgD,IAArBR,GAAoC,IAAV7G,IAAgB0E,EAKvEiC,EADEU,EACcR,EAEAD,EAAQ5G,EAAQyE,EAMlC,IAFA,IAAI6C,EAAc3K,KAAKmJ,MAAMa,GAEpBxH,EAAI,EAAGA,EAAI4G,EAAc5G,IAAK,CACrC,IAAIiE,OAAU,EAGZA,EADEjE,EAAImI,EAAc,EACV,IACDnI,EAAImI,IAAgB,EACG,KAArBX,EAAgBxH,GAEjB,EAGZiI,EAAYG,KAAKzJ,EAAAA,cAAoB+E,EAAc5C,EAAS,CAC1DN,IAAKR,EACL8D,MAAO9D,EACPmE,SAAUA,EACVJ,aAAcZ,EAAMnD,EAAImD,EAAMlD,QAC9B+D,WAAYkE,EAA2B5E,EAAYtD,EAAIqD,EAAKpD,QAAUoD,EAAKrD,EAAIqD,EAAKpD,QACpFgE,QAASA,EACTC,UAAWA,IACTC,GAAY,CACdC,QAAS/C,KAAKsE,YACdtB,YAAahD,KAAKqE,gBAClBT,YAAa5D,KAAKqE,gBAClBR,WAAY7D,KAAKsE,eAEpB,CAED,OAAOhH,EAAAA,cAAoB,OAAQmC,EAAS,CAC1CgH,GAAIA,EACJ7J,MAAOqD,EAAc,CAAC,EAAGrD,EAAO,CAC9B6E,QAAS,eACToB,UAAWA,IAEblG,UAAWA,EACX+J,SAAUA,EACV,aAAc1G,KAAKjD,MAAM,gBACvB+F,GAAY,CACdqB,aAAcnE,KAAKmE,eACjByC,EACL,KAGI9C,CACR,CAlLD,CAkLExG,EAAAA,eAEF,SAAS0J,IAAS,CAElBA,EAAKC,MAAQ,oBAEb,IAAIC,EAEJ,SAAU5E,GAGR,SAAS4E,EAAenK,GACtB,IAAIgH,EAUJ,OARA1F,EAAgB2B,KAAMkH,IAEtBnD,EAAQvC,EAA2BxB,KAAMe,EAAgBmG,GAAgBpH,KAAKE,KAAMjD,KAC9EiH,MAAQ,CACZxE,MAAOzC,EAAMoK,eAEfpD,EAAMqD,YAAcrD,EAAMqD,YAAYhD,KAAK/C,EAAuBA,EAAuB0C,KACzFA,EAAMsD,YAActD,EAAMsD,YAAYjD,KAAK/C,EAAuBA,EAAuB0C,KAClFA,CACR,CA6FD,OA3GArD,EAAUwG,EAAgB5E,GAgB1BlD,EAAa8H,EAAgB,CAAC,CAC5B/H,IAAK,4BACLK,MAAO,SAAmC+E,GACxCvE,KAAKyE,SAAS,CACZjF,MAAO+E,EAAU4C,eAEpB,GACA,CACDhI,IAAK,cACLK,MAAO,SAAqBA,EAAOiE,GACjC,IAAI6D,EAAStH,KAETuH,EAAWvH,KAAKwH,6BAA6BhI,GACjDQ,KAAKjD,MAAMgG,QAAQwE,GAEfvH,KAAKgE,MAAMxE,QAAU+H,GAEvBvH,KAAKyE,SAAS,CACZjF,MAAO+H,IACN,WACD,OAAOD,EAAOvK,MAAM0K,SAASH,EAAOtD,MAAMxE,MAC3C,GAEJ,GACA,CACDL,IAAK,cACLK,MAAO,SAAqByE,GAC1B,IAAIzE,OAAyBkI,IAAjBzD,EAA6BA,EAAejE,KAAKwH,6BAA6BvD,GAC1FjE,KAAKjD,MAAM6H,QAAQpF,EACpB,GACA,CACDL,IAAK,+BACLK,MAAO,SAAsCyE,GAC3C,IAAI0D,EAAkB1D,EAAejE,KAAKjD,MAAM6K,KAAO5H,KAAKjD,MAAM8K,MAElE,OAAOF,IAAoB3H,KAAKjD,MAAM8K,MAAQF,EAAkB,EAAI3H,KAAKjD,MAAMoI,UAAYwC,CAC5F,GACA,CACDxI,IAAK,8BACLK,MAAO,SAAqCA,GAC1C,YAAckI,IAAVlI,EACK,GAGDA,EAAQQ,KAAKjD,MAAM8K,OAAS7H,KAAKjD,MAAM6K,IAChD,GACA,CACDzI,IAAK,SACLK,MAAO,WACL,IAAIgD,EAAcxC,KAAKjD,MACnB6K,EAAOpF,EAAYoF,KACnBtB,EAAc9D,EAAY8D,YAC1BC,EAAa/D,EAAY+D,WACzBC,EAAoBhE,EAAYgE,kBAChC1D,EAAWN,EAAYM,SACvBsD,EAAQ5D,EAAY4D,MACpBjB,EAAY3C,EAAY2C,UACxBtC,EAAYL,EAAYK,UACxBgF,EAAQrF,EAAYqF,MACpBC,EAAOtF,EAAYsF,KACnBrB,EAAKjE,EAAYiE,GACjB9J,EAAY6F,EAAY7F,UACxBC,EAAQ4F,EAAY5F,MACpB8J,EAAWlE,EAAYkE,SAM3B,OAAOpJ,EAAAA,cAAoBwG,EAAQ,CACjC2C,GAAIA,EACJ7J,MAAOA,EACPD,UAAWA,EACX+J,SAAUA,EACV,aAAc1G,KAAKjD,MAAM,cACzBwI,aAVF,SAA+BsC,EAAOC,EAAMF,GAC1C,OAAOzL,KAAKmJ,OAAOwC,EAAOD,GAASD,EACpC,CAQeG,CAAsBF,EAAOC,EAAMF,GACjDpI,MAAOQ,KAAKgI,4BAA4BhI,KAAKgE,MAAMxE,OACnD6G,iBAAkBrG,KAAKgI,4BAA4BhI,KAAKjD,MAAMkL,mBAC9DnF,SAAUA,EACVsD,MAAOA,EACPjB,UAAWA,EACXtC,UAAWA,EACXyD,YAAaA,EACbC,WAAYA,EACZC,kBAAmBA,EACnBzD,QAAS/C,KAAKoH,YACdxC,QAAS5E,KAAKqH,aAEjB,KAGIH,CACR,CA7GD,CA6GE5J,EAAAA,eAEF4J,EAAepJ,aAAe,CAC5B+J,MAAO,EACPC,KAAM,EACNF,KAAM,EACN9E,UAAU,EACVsD,OAAO,EACPjB,UAAW,EACXtC,UAAW,MACX+B,QAASoC,EACTjE,QAASiE,EACTS,SAAUT,EACVV,YAAazE,EAAMC,MACnByE,WAAY1E,EAAMG,KAClBwE,kBAAmB3E,EAAMI,aAG3B,K", "sources": ["../node_modules/react-bootstrap/esm/ProgressBar.js", "../node_modules/react-rating/lib/react-rating.esm.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport { cloneElement } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { map } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ROUND_PRECISION = 1000;\n/**\n * Validate that children, if any, are instances of `<ProgressBar>`.\n */\n\nfunction onlyProgressBar(props, propName, componentName) {\n  const children = props[propName];\n\n  if (!children) {\n    return null;\n  }\n\n  let error = null;\n  React.Children.forEach(children, child => {\n    if (error) {\n      return;\n    }\n    /**\n     * Compare types in a way that works with libraries that patch and proxy\n     * components like react-hot-loader.\n     *\n     * see https://github.com/gaearon/react-hot-loader#checking-element-types\n     */\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n\n\n    const element = /*#__PURE__*/_jsx(ProgressBar, {});\n\n    if (child.type === element.type) return;\n    const childType = child.type;\n    const childIdentifier = /*#__PURE__*/React.isValidElement(child) ? childType.displayName || childType.name || childType : child;\n    error = new Error(`Children of ${componentName} can contain only ProgressBar ` + `components. Found ${childIdentifier}.`);\n  });\n  return error;\n}\n\nconst defaultProps = {\n  min: 0,\n  max: 100,\n  animated: false,\n  isChild: false,\n  visuallyHidden: false,\n  striped: false\n};\n\nfunction getPercentage(now, min, max) {\n  const percentage = (now - min) / (max - min) * 100;\n  return Math.round(percentage * ROUND_PRECISION) / ROUND_PRECISION;\n}\n\nfunction renderProgressBar({\n  min,\n  now,\n  max,\n  label,\n  visuallyHidden,\n  striped,\n  animated,\n  className,\n  style,\n  variant,\n  bsPrefix,\n  ...props\n}, ref) {\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...props,\n    role: \"progressbar\",\n    className: classNames(className, `${bsPrefix}-bar`, {\n      [`bg-${variant}`]: variant,\n      [`${bsPrefix}-bar-animated`]: animated,\n      [`${bsPrefix}-bar-striped`]: animated || striped\n    }),\n    style: {\n      width: `${getPercentage(now, min, max)}%`,\n      ...style\n    },\n    \"aria-valuenow\": now,\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    children: visuallyHidden ? /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    }) : label\n  });\n}\n\nconst ProgressBar = /*#__PURE__*/React.forwardRef(({\n  isChild,\n  ...props\n}, ref) => {\n  props.bsPrefix = useBootstrapPrefix(props.bsPrefix, 'progress');\n\n  if (isChild) {\n    return renderProgressBar(props, ref);\n  }\n\n  const {\n    min,\n    now,\n    max,\n    label,\n    visuallyHidden,\n    striped,\n    animated,\n    bsPrefix,\n    variant,\n    className,\n    children,\n    ...wrapperProps\n  } = props;\n  return /*#__PURE__*/_jsx(\"div\", {\n    ref: ref,\n    ...wrapperProps,\n    className: classNames(className, bsPrefix),\n    children: children ? map(children, child => /*#__PURE__*/cloneElement(child, {\n      isChild: true\n    })) : renderProgressBar({\n      min,\n      now,\n      max,\n      label,\n      visuallyHidden,\n      striped,\n      animated,\n      bsPrefix,\n      variant\n    }, ref)\n  });\n});\nProgressBar.displayName = 'ProgressBar';\nProgressBar.defaultProps = defaultProps;\nexport default ProgressBar;", "import React from 'react';\n\nfunction _typeof(obj) {\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n\n  return target;\n}\n\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\n\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}\n\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}\n\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (typeof call === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return _assertThisInitialized(self);\n}\n\nvar style = {\n  display: 'inline-block',\n  borderRadius: '50%',\n  border: '5px double white',\n  width: 30,\n  height: 30\n};\nvar Style = {\n  empty: _objectSpread({}, style, {\n    backgroundColor: '#ccc'\n  }),\n  full: _objectSpread({}, style, {\n    backgroundColor: 'black'\n  }),\n  placeholder: _objectSpread({}, style, {\n    backgroundColor: 'red'\n  })\n};\n\n// Return the corresponding React node for an icon.\nvar _iconNode = function _iconNode(icon) {\n  // If it is already a React Element just return it.\n  if (React.isValidElement(icon)) {\n    return icon;\n  } // If it is an object, try to use it as a CSS style object.\n\n\n  if (_typeof(icon) === 'object' && icon !== null) {\n    return React.createElement(\"span\", {\n      style: icon\n    });\n  } // If it is a string, use it as class names.\n\n\n  if (Object.prototype.toString.call(icon) === '[object String]') {\n    return React.createElement(\"span\", {\n      className: icon\n    });\n  }\n};\n\nvar RatingSymbol =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(RatingSymbol, _React$PureComponent);\n\n  function RatingSymbol() {\n    _classCallCheck(this, RatingSymbol);\n\n    return _possibleConstructorReturn(this, _getPrototypeOf(RatingSymbol).apply(this, arguments));\n  }\n\n  _createClass(RatingSymbol, [{\n    key: \"render\",\n    value: function render() {\n      var _iconContainerStyle;\n\n      var _this$props = this.props,\n          index = _this$props.index,\n          inactiveIcon = _this$props.inactiveIcon,\n          activeIcon = _this$props.activeIcon,\n          percent = _this$props.percent,\n          direction = _this$props.direction,\n          readonly = _this$props.readonly,\n          onClick = _this$props.onClick,\n          onMouseMove = _this$props.onMouseMove;\n\n      var backgroundNode = _iconNode(inactiveIcon);\n\n      var iconNode = _iconNode(activeIcon);\n\n      var iconContainerStyle = (_iconContainerStyle = {\n        display: 'inline-block',\n        position: 'absolute',\n        overflow: 'hidden',\n        top: 0\n      }, _defineProperty(_iconContainerStyle, direction === 'rtl' ? 'right' : 'left', 0), _defineProperty(_iconContainerStyle, \"width\", \"\".concat(percent, \"%\")), _iconContainerStyle);\n      var style = {\n        cursor: !readonly ? 'pointer' : 'inherit',\n        display: 'inline-block',\n        position: 'relative'\n      };\n\n      function handleMouseMove(e) {\n        if (onMouseMove) {\n          onMouseMove(index, e);\n        }\n      }\n\n      function handleMouseClick(e) {\n        if (onClick) {\n          // [Supporting both TouchEvent and MouseEvent](https://developer.mozilla.org/en-US/docs/Web/API/Touch_events/Supporting_both_TouchEvent_and_MouseEvent)\n          // We must prevent firing click event twice on touch devices.\n          e.preventDefault();\n          onClick(index, e);\n        }\n      }\n\n      return React.createElement(\"span\", {\n        style: style,\n        onClick: handleMouseClick,\n        onMouseMove: handleMouseMove,\n        onTouchMove: handleMouseMove,\n        onTouchEnd: handleMouseClick\n      }, backgroundNode, React.createElement(\"span\", {\n        style: iconContainerStyle\n      }, iconNode));\n    }\n  }]);\n\n  return RatingSymbol;\n}(React.PureComponent); // Define propTypes only in development. They will be void in production.\n\nvar Rating =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(Rating, _React$PureComponent);\n\n  function Rating(props) {\n    var _this;\n\n    _classCallCheck(this, Rating);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Rating).call(this, props));\n    _this.state = {\n      // Indicates the value that is displayed to the user in the form of symbols.\n      // It can be either 0 (for no displayed symbols) or (0, end]\n      displayValue: _this.props.value,\n      // Indicates if the user is currently hovering over the rating element\n      interacting: false\n    };\n    _this.onMouseLeave = _this.onMouseLeave.bind(_assertThisInitialized(_assertThisInitialized(_this)));\n    _this.symbolMouseMove = _this.symbolMouseMove.bind(_assertThisInitialized(_assertThisInitialized(_this)));\n    _this.symbolClick = _this.symbolClick.bind(_assertThisInitialized(_assertThisInitialized(_this)));\n    return _this;\n  }\n\n  _createClass(Rating, [{\n    key: \"componentWillReceiveProps\",\n    value: function componentWillReceiveProps(nextProps) {\n      var valueChanged = this.props.value !== nextProps.value;\n      this.setState(function (prevState) {\n        return {\n          displayValue: valueChanged ? nextProps.value : prevState.displayValue\n        };\n      });\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      // Ignore state update due to value changed from props.\n      // Usually originated through an onClick event.\n      if (prevProps.value !== this.props.value) {\n        return;\n      } // When hover ends, call this.props.onHover with no value.\n\n\n      if (prevState.interacting && !this.state.interacting) {\n        return this.props.onHover();\n      } // When hover over.\n\n\n      if (this.state.interacting) {\n        this.props.onHover(this.state.displayValue);\n      }\n    }\n  }, {\n    key: \"symbolClick\",\n    value: function symbolClick(symbolIndex, event) {\n      var value = this.calculateDisplayValue(symbolIndex, event);\n      this.props.onClick(value, event);\n    }\n  }, {\n    key: \"symbolMouseMove\",\n    value: function symbolMouseMove(symbolIndex, event) {\n      var value = this.calculateDisplayValue(symbolIndex, event); // This call should cause an update only if the state changes.\n      // Mainly the first time the mouse enters and whenever the value changes.\n      // So DidComponentUpdate is NOT called for every mouse movement.\n\n      this.setState({\n        interacting: !this.props.readonly,\n        displayValue: value\n      });\n    }\n  }, {\n    key: \"onMouseLeave\",\n    value: function onMouseLeave() {\n      this.setState({\n        displayValue: this.props.value,\n        interacting: false\n      });\n    }\n  }, {\n    key: \"calculateDisplayValue\",\n    value: function calculateDisplayValue(symbolIndex, event) {\n      var percentage = this.calculateHoverPercentage(event); // Get the closest top fraction.\n\n      var fraction = Math.ceil(percentage % 1 * this.props.fractions) / this.props.fractions; // Truncate decimal trying to avoid float precission issues.\n\n      var precision = Math.pow(10, 3);\n      var displayValue = symbolIndex + (Math.floor(percentage) + Math.floor(fraction * precision) / precision); // ensure the returned value is greater than 0 and lower than totalSymbols\n\n      return displayValue > 0 ? displayValue > this.props.totalSymbols ? this.props.totalSymbols : displayValue : 1 / this.props.fractions;\n    }\n  }, {\n    key: \"calculateHoverPercentage\",\n    value: function calculateHoverPercentage(event) {\n      var clientX = event.nativeEvent.type.indexOf(\"touch\") > -1 ? event.nativeEvent.type.indexOf(\"touchend\") > -1 ? event.changedTouches[0].clientX : event.touches[0].clientX : event.clientX;\n      var targetRect = event.target.getBoundingClientRect();\n      var delta = this.props.direction === 'rtl' ? targetRect.right - clientX : clientX - targetRect.left; // Returning 0 if the delta is negative solves the flickering issue\n\n      return delta < 0 ? 0 : delta / targetRect.width;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          readonly = _this$props.readonly,\n          quiet = _this$props.quiet,\n          totalSymbols = _this$props.totalSymbols,\n          value = _this$props.value,\n          placeholderValue = _this$props.placeholderValue,\n          direction = _this$props.direction,\n          emptySymbol = _this$props.emptySymbol,\n          fullSymbol = _this$props.fullSymbol,\n          placeholderSymbol = _this$props.placeholderSymbol,\n          className = _this$props.className,\n          id = _this$props.id,\n          style = _this$props.style,\n          tabIndex = _this$props.tabIndex;\n      var _this$state = this.state,\n          displayValue = _this$state.displayValue,\n          interacting = _this$state.interacting;\n      var symbolNodes = [];\n      var empty = [].concat(emptySymbol);\n      var full = [].concat(fullSymbol);\n      var placeholder = [].concat(placeholderSymbol);\n      var shouldDisplayPlaceholder = placeholderValue !== 0 && value === 0 && !interacting; // The value that will be used as base for calculating how to render the symbols\n\n      var renderedValue;\n\n      if (shouldDisplayPlaceholder) {\n        renderedValue = placeholderValue;\n      } else {\n        renderedValue = quiet ? value : displayValue;\n      } // The amount of full symbols\n\n\n      var fullSymbols = Math.floor(renderedValue);\n\n      for (var i = 0; i < totalSymbols; i++) {\n        var percent = void 0; // Calculate each symbol's fullness percentage\n\n        if (i - fullSymbols < 0) {\n          percent = 100;\n        } else if (i - fullSymbols === 0) {\n          percent = (renderedValue - i) * 100;\n        } else {\n          percent = 0;\n        }\n\n        symbolNodes.push(React.createElement(RatingSymbol, _extends({\n          key: i,\n          index: i,\n          readonly: readonly,\n          inactiveIcon: empty[i % empty.length],\n          activeIcon: shouldDisplayPlaceholder ? placeholder[i % full.length] : full[i % full.length],\n          percent: percent,\n          direction: direction\n        }, !readonly && {\n          onClick: this.symbolClick,\n          onMouseMove: this.symbolMouseMove,\n          onTouchMove: this.symbolMouseMove,\n          onTouchEnd: this.symbolClick\n        })));\n      }\n\n      return React.createElement(\"span\", _extends({\n        id: id,\n        style: _objectSpread({}, style, {\n          display: 'inline-block',\n          direction: direction\n        }),\n        className: className,\n        tabIndex: tabIndex,\n        \"aria-label\": this.props['aria-label']\n      }, !readonly && {\n        onMouseLeave: this.onMouseLeave\n      }), symbolNodes);\n    }\n  }]);\n\n  return Rating;\n}(React.PureComponent); // Define propTypes only in development.\n\nfunction noop() {}\n\nnoop._name = 'react_rating_noop';\n\nvar RatingAPILayer =\n/*#__PURE__*/\nfunction (_React$PureComponent) {\n  _inherits(RatingAPILayer, _React$PureComponent);\n\n  function RatingAPILayer(props) {\n    var _this;\n\n    _classCallCheck(this, RatingAPILayer);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(RatingAPILayer).call(this, props));\n    _this.state = {\n      value: props.initialRating\n    };\n    _this.handleClick = _this.handleClick.bind(_assertThisInitialized(_assertThisInitialized(_this)));\n    _this.handleHover = _this.handleHover.bind(_assertThisInitialized(_assertThisInitialized(_this)));\n    return _this;\n  }\n\n  _createClass(RatingAPILayer, [{\n    key: \"componentWillReceiveProps\",\n    value: function componentWillReceiveProps(nextProps) {\n      this.setState({\n        value: nextProps.initialRating\n      });\n    }\n  }, {\n    key: \"handleClick\",\n    value: function handleClick(value, e) {\n      var _this2 = this;\n\n      var newValue = this.translateDisplayValueToValue(value);\n      this.props.onClick(newValue); // Avoid calling setState if not necessary. Micro optimisation.\n\n      if (this.state.value !== newValue) {\n        // If we have a new value trigger onChange callback.\n        this.setState({\n          value: newValue\n        }, function () {\n          return _this2.props.onChange(_this2.state.value);\n        });\n      }\n    }\n  }, {\n    key: \"handleHover\",\n    value: function handleHover(displayValue) {\n      var value = displayValue === undefined ? displayValue : this.translateDisplayValueToValue(displayValue);\n      this.props.onHover(value);\n    }\n  }, {\n    key: \"translateDisplayValueToValue\",\n    value: function translateDisplayValueToValue(displayValue) {\n      var translatedValue = displayValue * this.props.step + this.props.start; // minimum value cannot be equal to start, since it's exclusive\n\n      return translatedValue === this.props.start ? translatedValue + 1 / this.props.fractions : translatedValue;\n    }\n  }, {\n    key: \"tranlateValueToDisplayValue\",\n    value: function tranlateValueToDisplayValue(value) {\n      if (value === undefined) {\n        return 0;\n      }\n\n      return (value - this.props.start) / this.props.step;\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props = this.props,\n          step = _this$props.step,\n          emptySymbol = _this$props.emptySymbol,\n          fullSymbol = _this$props.fullSymbol,\n          placeholderSymbol = _this$props.placeholderSymbol,\n          readonly = _this$props.readonly,\n          quiet = _this$props.quiet,\n          fractions = _this$props.fractions,\n          direction = _this$props.direction,\n          start = _this$props.start,\n          stop = _this$props.stop,\n          id = _this$props.id,\n          className = _this$props.className,\n          style = _this$props.style,\n          tabIndex = _this$props.tabIndex;\n\n      function calculateTotalSymbols(start, stop, step) {\n        return Math.floor((stop - start) / step);\n      }\n\n      return React.createElement(Rating, {\n        id: id,\n        style: style,\n        className: className,\n        tabIndex: tabIndex,\n        \"aria-label\": this.props['aria-label'],\n        totalSymbols: calculateTotalSymbols(start, stop, step),\n        value: this.tranlateValueToDisplayValue(this.state.value),\n        placeholderValue: this.tranlateValueToDisplayValue(this.props.placeholderRating),\n        readonly: readonly,\n        quiet: quiet,\n        fractions: fractions,\n        direction: direction,\n        emptySymbol: emptySymbol,\n        fullSymbol: fullSymbol,\n        placeholderSymbol: placeholderSymbol,\n        onClick: this.handleClick,\n        onHover: this.handleHover\n      });\n    }\n  }]);\n\n  return RatingAPILayer;\n}(React.PureComponent);\n\nRatingAPILayer.defaultProps = {\n  start: 0,\n  stop: 5,\n  step: 1,\n  readonly: false,\n  quiet: false,\n  fractions: 1,\n  direction: 'ltr',\n  onHover: noop,\n  onClick: noop,\n  onChange: noop,\n  emptySymbol: Style.empty,\n  fullSymbol: Style.full,\n  placeholderSymbol: Style.placeholder\n}; // Define propTypes only in development.\n\nexport default RatingAPILayer;\n"], "names": ["getPercentage", "now", "min", "max", "percentage", "Math", "round", "renderProgressBar", "ref", "label", "visuallyHidden", "striped", "animated", "className", "style", "variant", "bsPrefix", "props", "_jsx", "role", "classNames", "width", "children", "ProgressBar", "React", "<PERSON><PERSON><PERSON><PERSON>", "useBootstrapPrefix", "wrapperProps", "map", "child", "cloneElement", "displayName", "defaultProps", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "_defineProperty", "value", "_extends", "assign", "arguments", "source", "hasOwnProperty", "call", "apply", "this", "_objectSpread", "ownKeys", "keys", "getOwnPropertySymbols", "concat", "filter", "sym", "getOwnPropertyDescriptor", "for<PERSON>ach", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "_getPrototypeOf", "o", "setPrototypeOf", "getPrototypeOf", "__proto__", "p", "_assertThisInitialized", "self", "ReferenceError", "_possibleConstructorReturn", "display", "borderRadius", "border", "height", "Style", "empty", "backgroundColor", "full", "placeholder", "_iconNode", "icon", "toString", "RatingSymbol", "_React$PureComponent", "_iconContainerStyle", "_this$props", "index", "inactiveIcon", "activeIcon", "percent", "direction", "readonly", "onClick", "onMouseMove", "backgroundNode", "iconNode", "iconContainerStyle", "position", "overflow", "top", "cursor", "handleMouseMove", "e", "handleMouseClick", "preventDefault", "onTouchMove", "onTouchEnd", "Rating", "_this", "state", "displayValue", "interacting", "onMouseLeave", "bind", "symbolMouseMove", "symbolClick", "nextProps", "valueChanged", "setState", "prevState", "prevProps", "onHover", "symbolIndex", "event", "calculateDisplayValue", "calculateHoverPercentage", "fraction", "ceil", "fractions", "precision", "pow", "floor", "totalSymbols", "clientX", "nativeEvent", "type", "indexOf", "changedTouches", "touches", "targetRect", "getBoundingClientRect", "delta", "right", "left", "renderedValue", "quiet", "placeholder<PERSON><PERSON><PERSON>", "emptySymbol", "fullSymbol", "placeholderSymbol", "id", "tabIndex", "_this$state", "symbolNodes", "shouldDisplayPlaceholder", "fullSymbols", "push", "noop", "_name", "Rating<PERSON><PERSON><PERSON><PERSON>", "initialRating", "handleClick", "handleHover", "_this2", "newValue", "translateDisplayValueToValue", "onChange", "undefined", "translatedValue", "step", "start", "stop", "calculateTotalSymbols", "tranlateValueToDisplayValue", "placeholderRating"], "sourceRoot": ""}