{"version": 3, "file": "static/js/8485.195abccc.chunk.js", "mappings": "8RAMaA,EAAb,0CAEE,aAAe,IAAD,yBACZ,gBAFFC,qBAAuBC,EAAAA,YACT,EAyFdC,gBAAkB,SAACC,GACbC,OAAOC,QAAQ,qCAAuCF,EAAIG,QAAU,OACtE,EAAKC,SAAS,CACZC,eAAgB,EAAKC,MAAMD,eAAeE,OAAO,CAC/CC,MAAO,YACPC,MAAOT,EAAIU,KACXC,OAAQX,EAAIW,UAInB,EAjGC,EAAKL,MAAQ,CACXM,kBAAkB,EAClBP,eAAgB,CACd,CAAEG,MAAO,YAAaC,MAAO,IAAII,QALzB,CAQb,CAVH,qCAWE,WACE,OACE,0BACA,iBAAKC,UAAU,MAAf,WACE,iBAAKA,UAAU,WAAf,WACE,iBAAKA,UAAU,qBAAf,WACE,iBAAKA,UAAU,WAAf,WACE,kFACA,cAAGA,UAAU,gBACb,cAAGA,UAAU,kBAAb,yBAEF,iBAAKA,UAAU,WAAf,WACE,uDACA,cAAGA,UAAU,gBACb,cAAGA,UAAU,kBAAb,6BAEF,iBAAKA,UAAU,WAAf,WACE,mFACA,cAAGA,UAAU,gBACb,cAAGA,UAAU,kBAAb,2BAGJ,iBAAKA,UAAU,OAAf,WACE,yCACA,gBAAKA,UAAU,gCAAf,UACE,mBAAOA,UAAU,mBAAjB,WACE,kBAAOC,KAAK,WAAWD,UAAU,mBAAmBE,gBAAc,KAClE,cAAGF,UAAU,iBAFf,sBAMF,gBAAKA,UAAU,+BAAf,UACE,mBAAOA,UAAU,mBAAjB,WACE,kBAAOC,KAAK,WAAWD,UAAU,mBAAmBE,gBAAc,KAClE,cAAGF,UAAU,iBAFf,qBAMF,gBAAKA,UAAU,6BAAf,UACE,mBAAOA,UAAU,mBAAjB,WACE,kBAAOC,KAAK,WAAWD,UAAU,mBAAmBE,gBAAc,KAClE,cAAGF,UAAU,iBAFf,sBAMF,gBAAKA,UAAU,gCAAf,UACE,mBAAOA,UAAU,mBAAjB,WACE,kBAAOC,KAAK,WAAWD,UAAU,mBAAmBE,gBAAc,KAClE,cAAGF,UAAU,iBAFf,4BAQN,gBAAKA,UAAU,WAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,2BAEA,SAAC,IAAD,CACEG,YAAY,eACZC,OAAQ,CACNC,KAAM,kBACNC,OAAQ,QACRC,MAAO,kDAETC,QAAS,CAAEC,EAAAA,GAAeC,EAAAA,GAAgBC,EAAAA,IAC1CC,IAAMC,KAAK9B,qBACX+B,SAAWD,KAAKrB,MAAMM,iBACtBiB,OAASF,KAAKrB,MAAMD,eACpByB,UAAYH,KAAK5B,6BAQ9B,KAzFH,GAA8BgC,EAAAA,WAwG9B,W", "sources": ["app/apps/Calendar.js"], "sourcesContent": ["import React, { Component } from 'react'\nimport FullCalendar from '@fullcalendar/react'\nimport dayGridPlugin from '@fullcalendar/daygrid'\nimport timeGridPlugin from '@fullcalendar/timegrid'\nimport interactionPlugin from '@fullcalendar/interaction'\n\nexport class Calendar extends Component {\n  calendarComponentRef = React.createRef();\n  constructor() {\n    super();\n    this.state = {\n      calendarWeekends: true,\n      calendarEvents: [\n        { title: 'Event Now', start: new Date() }\n      ]\n    }\n  }\n  render() {\n    return (\n      <div>\n      <div className=\"row\">\n        <div className=\"col-md-3\">\n          <div className=\"fc-external-events\">\n            <div className='fc-event'>\n              <p>Deciphering Marketing Lingo For Small Business Owners</p>\n              <p className=\"small-text\"></p>\n              <p className=\"text-muted mb-0\">Georgia</p>\n            </div>\n            <div className='fc-event'>\n              <p>Influencing The Influencer</p>\n              <p className=\"small-text\"></p>\n              <p className=\"text-muted mb-0\">Netherlands</p>\n            </div>\n            <div className='fc-event'>\n              <p>Profiles Of The Powerful Advertising Exec Steve Grasse</p>\n              <p className=\"small-text\"></p>\n              <p className=\"text-muted mb-0\">Canada</p>\n            </div>\n          </div>\n          <div className=\"mt-4\">\n            <p>Filter board</p>\n            <div className=\"form-check form-check-primary\">\n              <label className=\"form-check-label\">\n                <input type=\"checkbox\" className=\"form-check-input\" defaultChecked />\n                <i className=\"input-helper\"></i>\n                Project Board\n              </label>\n            </div>\n            <div className=\"form-check form-check-danger\">\n              <label className=\"form-check-label\">\n                <input type=\"checkbox\" className=\"form-check-input\" defaultChecked />\n                <i className=\"input-helper\"></i>\n                Kanban Board\n              </label>\n            </div>\n            <div className=\"form-check form-check-info\">\n              <label className=\"form-check-label\">\n                <input type=\"checkbox\" className=\"form-check-input\" defaultChecked />\n                <i className=\"input-helper\"></i>\n                Summary Board\n              </label>\n            </div>\n            <div className=\"form-check form-check-success\">\n              <label className=\"form-check-label\">\n                <input type=\"checkbox\" className=\"form-check-input\" defaultChecked />\n                <i className=\"input-helper\"></i>\n                Planner Board\n              </label>\n            </div>\n          </div>\n        </div>\n        <div className=\"col-md-9\">\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <h4 className=\"card-title\">Fullcalendar</h4>\n              {/* <FullCalendar defaultView=\"dayGridMonth\" plugins={[ dayGridPlugin ]} /> */}\n              <FullCalendar\n                defaultView=\"dayGridMonth\"\n                header={{\n                  left: 'prev,next today',\n                  center: 'title',\n                  right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'\n                }}\n                plugins={[ dayGridPlugin, timeGridPlugin, interactionPlugin ]}\n                ref={ this.calendarComponentRef }\n                weekends={ this.state.calendarWeekends }\n                events={ this.state.calendarEvents }\n                dateClick={ this.handleDateClick }\n              />         \n            </div>\n          </div>\n        </div>\n      </div>\n      </div>\n    )\n  }\n\n  handleDateClick = (arg) => {\n    if (window.confirm('Would you like to add an event to ' + arg.dateStr + ' ?')) {\n      this.setState({  // add new event data\n        calendarEvents: this.state.calendarEvents.concat({ // creates a new array\n          title: 'New Event',\n          start: arg.date,\n          allDay: arg.allDay\n        })\n      })\n    }\n  }\n}\n\nexport default Calendar\n"], "names": ["Calendar", "calendarComponentRef", "React", "handleDateClick", "arg", "window", "confirm", "dateStr", "setState", "calendarEvents", "state", "concat", "title", "start", "date", "allDay", "calendarWeekends", "Date", "className", "type", "defaultChecked", "defaultView", "header", "left", "center", "right", "plugins", "dayGridPlugin", "timeGridPlugin", "interactionPlugin", "ref", "this", "weekends", "events", "dateClick", "Component"], "sourceRoot": ""}