{"version": 3, "file": "static/js/8681.48ecaeae.chunk.js", "mappings": "mUAgBMA,EAAY,CAChBC,UAAW,SAGAC,EAAb,0IACE,WACE,OACE,0BACE,gBAAKC,UAAU,MAAf,UACE,gBAAKA,UAAU,qBAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,yBACA,iBAAKA,UAAU,MAAf,WACE,iBAAKA,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,wBACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,OACLC,MAAM,UACNC,KAAK,cACLC,SAAUC,KAAKD,SACfE,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,27BA6BT,iBAAKjB,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,8BACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,OACLC,MAAM,UACNC,KAAK,mBACLG,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,4cAmBX,iBAAKjB,UAAU,MAAf,WACE,iBAAKA,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,uBACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,MACLC,MAAM,UACNC,KAAK,YACLG,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,2PAmBT,iBAAKjB,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,wBACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,OACLC,MAAM,UACNC,KAAK,aACLG,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,gQAqBX,iBAAKjB,UAAU,MAAf,WACE,iBAAKA,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,wBACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,OACLC,MAAM,UACNC,KAAK,aACLG,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,wkBAgCT,iBAAKjB,UAAU,uBAAf,WACE,eAAIA,UAAU,gBAAd,uBACA,SAAC,KAAD,CAAWC,MAAOJ,EAChBK,KAAK,MACLC,MAAM,UACNC,KAAK,YACLG,YAAa,CAAEC,iBAAiB,GAChCC,YAAY,mBACZC,OAAQJ,KAAKI,OACbC,SAAU,GACVC,iBAAiB,EACjBC,YAAY,EACZC,qBAAqB,EACrBC,OAAO,OACPC,MAAM,OACNC,MAAK,0ZAgCxB,KA/PH,GAAgCC,EAAAA,WAkQhC,W", "sources": ["app/editors/CodeEditor.js"], "sourcesContent": ["import React, { Component } from 'react';\n// import brace from \"brace\";\n\nimport AceEditor from \"react-ace\";\n\n// Import a Mode (language)\nimport \"brace/mode/java\";\nimport \"brace/mode/html\";\nimport \"brace/mode/sass\";\nimport \"brace/mode/json\";\nimport \"brace/mode/php\";\n\n// Import a Theme (okadia, github, xcode etc)\nimport \"brace/theme/github\";\nimport \"brace/theme/monokai\";\n\nconst aceHeight = {\n  minHeight: \"300px\"\n};\n\nexport class CodeEditor extends Component {\n  render() {\n    return (\n      <div> \n        <div className=\"row\">\n          <div className=\"col-12 grid-margin\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Ace Editor</h4>\n                <div className=\"row\">\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">HTML Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"html\"\n                      theme=\"monokai\"\n                      name=\"firstEditor\"\n                      onChange={this.onChange}\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n<div className=\"panel panel-default\">\n  <div className=\"panel-heading\">\n    <h5 className=\"panel-title\">\n      Panel Title\n      <span className=\"text-semibold\">Default</span>\n      <small>Full featured toolbar</small>\n    </h5>\n    <ul className=\"panel-heading-icons\">\n      <li>\n        <a href=\"!#\" onClick={event => event.preventDefault()} data-panel=\"collapse\"><i className=\"icon-arrow-down2\"></i></a>\n      </li>\n      <li>\n        <a href=\"!#\" onClick={event => event.preventDefault()} data-panel=\"reload\"><i className=\"icon-reload\"></i></a>\n      </li>\n      <li>\n        <a href=\"!#\" onClick={event => event.preventDefault()} data-panel=\"move\"><i className=\"icon-move\"></i></a>\n      </li>\n      <li>\n        <a href=\"!#\" onClick={event => event.preventDefault()} data-panel=\"close\"><i className=\"icon-close\"></i></a>\n      </li>\n    </ul>\n  </div>\n  <div className=\"panel-body\">\n    Panel body\n  </div>\n</div>`}\n                    />\n                  </div>\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">Javascript Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"java\"\n                      theme=\"monokai\"\n                      name=\"javaeditorEditor\"\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n/**\n  * In fact, you're looking at ACE right now. Go ahead and play with it!\n  *\n  * We are currently showing off the JavaScript mode. ACE has support for 45\n  * language modes and 24 color themes!\n*/\n    \n    function add(x, y) {\n      var resultString = \"Hello, ACE! The result of your math is: \";\n      var result = x + y;\n      return resultString + result;\n    }\n    \n    var addResult = add(3, 2);\n    console.log(addResult);`}\n                      />\n                  </div>\n                </div>\n                <div className=\"row\">\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">CSS Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"css\"\n                      theme=\"monokai\"\n                      name=\"cssEditor\"\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n  .nav ul {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n  }\n  \n  .nav li {\n    display: inline-block;\n  }\n  \n  .nav a {\n    display: block;\n    padding: 6px 12px;\n    text-decoration: none;\n  }\n                      `}\n                    />\n                  </div>\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">scss Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"sass\"\n                      theme=\"monokai\"\n                      name=\"sassEditor\"\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n  .nav {\n    ul {\n      margin: 0;\n      padding: 0;\n      list-style: none;\n    }\n  \n    li {\n      display: inline-block;\n    }\n  \n    a {\n      display: block;\n      padding: 6px 12px;\n      text-decoration: none;\n    }\n  }`}\n                      />\n                  </div>\n                </div>\n                <div className=\"row\">\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">Json Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"json\"\n                      theme=\"monokai\"\n                      name=\"jsonEditor\"\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n  {\n    \"firstName\": \"John\",\n    \"lastName\": \"Smith\",\n    \"isAlive\": true,\n    \"age\": 27,\n    \"address\": {\n    \"streetAddress\": \"21 2nd Street\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"postalCode\": \"10021-3100\"\n    },\n    \"phoneNumbers\": [\n      {\n        \"type\": \"home\",\n        \"number\": \"************\"\n      },\n      {\n        \"type\": \"office\",\n        \"number\": \"************\"\n      },\n      {\n        \"type\": \"mobile\",\n        \"number\": \"************\"\n      }\n    ],\n    \"children\": [],\n    \"spouse\": null\n  }\n                      `}\n                    />\n                  </div>\n                  <div className=\"col-md-6 grid-margin\">\n                    <h5 className=\"card-subtitle\">PHP Mode</h5>\n                    <AceEditor style={aceHeight}\n                      mode=\"php\"\n                      theme=\"monokai\"\n                      name=\"phpEditor\"\n                      editorProps={{ $blockScrolling: true }}\n                      placeholder=\"Placeholder Text\"\n                      onLoad={this.onLoad}\n                      fontSize={14}\n                      showPrintMargin={true}\n                      showGutter={true}\n                      highlightActiveLine={true}\n                      height=\"100%\"\n                      width=\"100%\"\n                      value={`\n  <?php\n\n  function nfact($n) {\n    if ($n == 0) {\n      return 1;\n    }\n    else {\n      return $n * nfact($n - 1);\n    }\n  }\n\n  echo \"\\n\\nPlease enter a whole number ... \";\n  $num = trim(fgets(STDIN));\n\n\n  // ===== PROCESS - Determing the factorial of the input number =====\n\n  $output = \"\\n\\nFactorial \" . $num . \" = \" . nfact($num) . \"\\n\\n\";\n  echo $output;\n\n?>\n`}\n                      />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n\nexport default CodeEditor\n"], "names": ["aceHeight", "minHeight", "CodeEditor", "className", "style", "mode", "theme", "name", "onChange", "this", "editorProps", "$blockScrolling", "placeholder", "onLoad", "fontSize", "showPrintMargin", "showGutter", "highlightActiveLine", "height", "width", "value", "Component"], "sourceRoot": ""}