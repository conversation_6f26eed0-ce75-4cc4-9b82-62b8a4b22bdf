{"version": 3, "file": "static/js/8443.f7d474cf.chunk.js", "mappings": "uPAEaA,EAAb,0IACE,WACE,OACE,0BACE,iBAAKC,UAAU,MAAf,WACE,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,uBACA,eAAGA,UAAU,mBAAb,wBAA0C,mCAA1C,QAAqE,mCAArE,cAAsG,kCAAtG,QAA0H,sCAE1H,iBAAKA,UAAU,gBAAf,WACE,yCACA,yCACA,yCACA,yCACA,yCACA,mDAKR,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,2CACA,cAAGA,UAAU,mBAAb,qDACA,iBAAKA,UAAU,gBAAf,WACE,2CAAiB,kBAAOA,UAAU,aAAjB,kCAEjB,2CAAiB,kBAAOA,UAAU,aAAjB,kCAEjB,2CAAiB,kBAAOA,UAAU,aAAjB,kCAEjB,2CAAiB,kBAAOA,UAAU,aAAjB,kCAEjB,2CAAiB,kBAAOA,UAAU,aAAjB,kCAEjB,2CAAiB,kBAAOA,UAAU,aAAjB,4CAMzB,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,+BACA,eAAGA,UAAU,mBAAb,yBAA2C,wCAA3C,QAAqE,6CAErE,iBAAKA,UAAU,gBAAf,WACE,eAAIA,UAAU,YAAd,wBACA,eAAIA,UAAU,YAAd,wBACA,eAAIA,UAAU,YAAd,wBACA,eAAIA,UAAU,YAAd,kCAKR,gBAAKA,UAAU,sCAAf,UACE,iBAAKA,UAAU,MAAf,WACE,gBAAKA,UAAU,qCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,wBACA,eAAGA,UAAU,mBAAb,6BAA+C,kCAA/C,YACA,6PAIN,gBAAKA,UAAU,qCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,wBACA,eAAGA,UAAU,mBAAb,yBAA2C,uCAA3C,MAAkE,uCAAlE,MAAyF,2CAEzF,iBAAKA,UAAU,MAAf,WACE,gBAAKA,UAAU,qCAAf,UACE,iBAAKA,UAAU,qCAAf,WACE,cAAGA,UAAU,qCACb,cAAGA,UAAU,YAAb,6BAGJ,gBAAKA,UAAU,qCAAf,UACE,iBAAKA,UAAU,qCAAf,WACE,cAAGA,UAAU,qCACb,cAAGA,UAAU,YAAb,6BAGJ,gBAAKA,UAAU,qCAAf,UACE,iBAAKA,UAAU,qCAAf,WACE,cAAGA,UAAU,oCACb,cAAGA,UAAU,YAAb,4CAShB,gBAAKA,UAAU,oCAAf,UACE,iBAAKA,UAAU,OAAf,WACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,0BACA,eAAGA,UAAU,mBAAb,kCAAoD,sEAEpD,uBAAYA,UAAU,aAAtB,UACE,cAAGA,UAAU,OAAb,yGAGJ,gBAAKA,UAAU,YAAf,UACE,wBAAYA,UAAU,gCAAtB,WACE,kHACA,oBAAQA,UAAU,+BAAlB,gCAAmE,iBAAMC,MAAM,eAAZ,wCAK3E,gBAAKD,UAAU,oCAAf,UACE,iBAAKA,UAAU,OAAf,WACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,sBACA,eAAGA,UAAU,mBAAb,mBAAqC,wCAArC,YACA,iBAAKA,UAAU,MAAf,WACE,gBAAKA,UAAU,WAAf,UACE,gCACE,cAAGA,UAAU,mBAAb,4BACA,4CACA,uCACA,2DAGJ,gBAAKA,UAAU,WAAf,UACE,qBAASA,UAAU,eAAnB,WACE,cAAGA,UAAU,mBAAb,uBACA,cAAGA,UAAU,OAAb,yCACA,cAAGA,UAAU,mBAAb,4BACA,2DAKR,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,mBACA,eAAGA,UAAU,mBAAb,yBAA2C,wCAE3C,cAAGA,UAAU,OAAb,+FAIN,gBAAKA,UAAU,wBAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,0BACA,eAAGA,UAAU,mBAAb,yBAA2C,4CAA3C,MAAuE,8CAAvE,sCACA,iBAAKA,UAAU,MAAf,WACE,iBAAKA,UAAU,WAAf,WACE,cAAGA,UAAU,eAAb,4BACA,cAAGA,UAAU,eAAb,4BACA,cAAGA,UAAU,cAAb,2BACA,cAAGA,UAAU,eAAb,4BACA,cAAGA,UAAU,YAAb,4BAEF,iBAAKA,UAAU,WAAf,WACE,cAAGA,UAAU,0BAAb,0BACA,cAAGA,UAAU,iBAAb,8BACA,cAAGA,UAAU,YAAb,yBACA,cAAGA,UAAU,aAAb,0BACA,cAAGA,UAAU,0BAAb,uCAMV,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,gCACA,iBAAKA,UAAU,QAAf,WACE,cAAGA,UAAU,6DACb,gBAAKA,UAAU,aAAf,UACE,cAAGA,UAAU,YAAb,oGAMV,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,mCACA,iBAAKA,UAAU,QAAf,WACE,cAAGA,UAAU,8DACb,gBAAKA,UAAU,aAAf,UACE,cAAGA,UAAU,YAAb,oGAMV,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,mCACA,iBAAKA,UAAU,QAAf,WACE,cAAGA,UAAU,2DACb,gBAAKA,UAAU,aAAf,UACE,cAAGA,UAAU,YAAb,oGAMV,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,+BACA,eAAGA,UAAU,mBAAb,gCAAkD,qCAAlD,0BACA,2CAAiB,iBAAMA,UAAU,wBAAhB,yBAAjB,oMAIN,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,6BACA,2BACE,wDACA,yDACA,6DACA,+DACA,oEAKR,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,wBACA,eAAGA,UAAU,mBAAb,wBAA0C,oDAE1C,2CAAiB,iBAAMA,UAAU,mBAAhB,8BAAjB,+LAIN,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,2BACA,2BACE,wDACA,yDACA,6DACA,+DACA,qEAKR,gBAAKA,UAAU,oCAAf,UACE,iBAAKA,UAAU,OAAf,WACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,0BAAd,wBACA,eAAGA,UAAU,mBAAb,uBAAyC,kCAAzC,0BACA,wBACE,iIAGJ,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,yBAAd,wBACA,eAAGA,UAAU,mBAAb,yBAA2C,kDAE3C,cAAGA,UAAU,iBAAb,mHAEF,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,0BAAd,wBACA,eAAGA,UAAU,mBAAb,yBAA2C,kDAE3C,cAAGA,UAAU,iBAAb,wHAIN,gBAAKA,UAAU,oCAAf,UACE,iBAAKA,UAAU,OAAf,WACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,mBACA,eAAGA,UAAU,mBAAb,yBAA2C,8CAE3C,cAAGA,UAAU,aAAb,mHAEF,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,0BAAd,qBACA,eAAGA,UAAU,mBAAb,+BAAiD,oCAAjD,YACA,wBACE,qIAGJ,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,uBAAd,0BACA,eAAGA,UAAU,mBAAb,yBAA2C,mDAE3C,cAAGA,UAAU,kBAAb,wHAIN,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,6BACA,eAAGA,UAAU,mBAAb,wBAA0C,2CAA1C,QAAuE,uCACvE,gBAAIA,UAAU,cAAd,WACE,wDACA,yDACA,6DACA,+DACA,qEAKR,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,6BACA,eAAGA,UAAU,mBAAb,wBAA0C,0CAA1C,QAAsE,uCACtE,gBAAIA,UAAU,aAAd,WACE,wDACA,yDACA,6DACA,+DACA,qEAKR,gBAAKA,UAAU,oCAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,6BACA,eAAGA,UAAU,mBAAb,wBAA0C,yCAA1C,QAAqE,uCACrE,gBAAIA,UAAU,YAAd,WACE,wDACA,yDACA,6DACA,+DACA,wEAQf,KAnWH,GAAgCE,EAAAA,WAsWhC,W", "sources": ["app/basic-ui/Typography.js"], "sourcesContent": ["import React, { Component } from 'react'\n\nexport class Typography extends Component {\n  render() {\n    return (\n      <div>\n        <div className=\"row\">\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Headings</h4>\n                <p className=\"card-description\"> Add tags <code>&lt;h1&gt;</code> to <code>&lt;h6&gt;</code> or class <code>.h1</code> to <code>.h6</code>\n                </p>\n                <div className=\"template-demo\">\n                  <h1>h1. Heading</h1>\n                  <h2>h2. Heading</h2>\n                  <h3>h3. Heading</h3>\n                  <h4>h4. Heading</h4>\n                  <h5>h5. Heading</h5>\n                  <h6>h6. Heading</h6>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Headings with secondary text</h4>\n                <p className=\"card-description\"> Add faded secondary text to headings </p>\n                <div className=\"template-demo\">\n                  <h1> h1. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h1>\n                  <h2> h2. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h2>\n                  <h3> h3. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h3>\n                  <h4> h4. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h4>\n                  <h5> h5. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h5>\n                  <h6> h6. Heading <small className=\"text-muted\"> Secondary text </small>\n                  </h6>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Display headings</h4>\n                <p className=\"card-description\"> Add class <code>.display1</code> to <code>.display-4</code>\n                </p>\n                <div className=\"template-demo\">\n                  <h1 className=\"display-1\">Display 1</h1>\n                  <h1 className=\"display-2\">Display 2</h1>\n                  <h1 className=\"display-3\">Display 3</h1>\n                  <h1 className=\"display-4\">Display 4</h1>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 d-flex align-items-stretch\">\n            <div className=\"row\">\n              <div className=\"col-md-12 grid-margin stretch-card\">\n                <div className=\"card\">\n                  <div className=\"card-body\">\n                    <h4 className=\"card-title\">Paragraph</h4>\n                    <p className=\"card-description\"> Write text in <code>&lt;p&gt;</code> tag </p>\n                    <p> Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley not only five centuries, </p>\n                  </div>\n                </div>\n              </div>\n              <div className=\"col-md-12 grid-margin stretch-card\">\n                <div className=\"card\">\n                  <div className=\"card-body\">\n                    <h4 className=\"card-title\">Icon size</h4>\n                    <p className=\"card-description\"> Add class <code>.icon-lg</code>, <code>.icon-md</code>, <code>.icon-sm</code>\n                    </p>\n                    <div className=\"row\">\n                      <div className=\"col-md-4 d-flex align-items-center\">\n                        <div className=\"d-flex flex-row align-items-center\">\n                          <i className=\"ti-package icon-lg text-warning\"></i>\n                          <p className=\"mb-0 ms-1\"> Icon-lg </p>\n                        </div>\n                      </div>\n                      <div className=\"col-md-4 d-flex align-items-center\">\n                        <div className=\"d-flex flex-row align-items-center\">\n                          <i className=\"ti-package icon-md text-success\"></i>\n                          <p className=\"mb-0 ms-1\"> Icon-md </p>\n                        </div>\n                      </div>\n                      <div className=\"col-md-4 d-flex align-items-center\">\n                        <div className=\"d-flex flex-row align-items-center\">\n                          <i className=\"ti-package icon-sm text-danger\"></i>\n                          <p className=\"mb-0 ms-1\"> Icon-sm </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Blockquotes</h4>\n                <p className=\"card-description\"> Wrap content inside<code>&lt;blockquote className=\"blockquote\"&gt;</code>\n                </p>\n                <blockquote className=\"blockquote\">\n                  <p className=\"mb-0\">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>\n                </blockquote>\n              </div>\n              <div className=\"card-body\">\n                <blockquote className=\"blockquote blockquote-primary\">\n                  <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer posuere erat a ante.</p>\n                  <footer className=\"blockquote-footer text-muted\">Someone famous in <cite title=\"Source Title\">Source Title</cite></footer>\n                </blockquote>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Address</h4>\n                <p className=\"card-description\"> Use <code>&lt;address&gt;</code> tag </p>\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <address>\n                      <p className=\"font-weight-bold\">staradmin imc</p>\n                      <p> 695 lsom Ave, </p>\n                      <p> Suite 00 </p>\n                      <p> San Francisco, CA 94107 </p>\n                    </address>\n                  </div>\n                  <div className=\"col-md-6\">\n                    <address className=\"text-primary\">\n                      <p className=\"font-weight-bold\"> E-mail </p>\n                      <p className=\"mb-2\"> <EMAIL> </p>\n                      <p className=\"font-weight-bold\"> Web Address </p>\n                      <p> www.staradmin.com </p>\n                    </address>\n                  </div>\n                </div>\n              </div>\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Lead</h4>\n                <p className=\"card-description\"> Use class <code>.lead</code>\n                </p>\n                <p className=\"lead\"> Vivamus sagittis lacus vel augue laoreet rutrum faucibus dolor auctor. </p>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-12 grid-margin\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Text colors</h4>\n                <p className=\"card-description\"> Use class <code>.text-primary</code>, <code>.text-secondary</code> etc. for text in theme colors </p>\n                <div className=\"row\">\n                  <div className=\"col-md-6\">\n                    <p className=\"text-primary\">.text-primary</p>\n                    <p className=\"text-success\">.text-success</p>\n                    <p className=\"text-danger\">.text-danger</p>\n                    <p className=\"text-warning\">.text-warning</p>\n                    <p className=\"text-info\">.text-info</p>\n                  </div>\n                  <div className=\"col-md-6\">\n                    <p className=\"text-light bg-dark pl-1\">.text-light</p>\n                    <p className=\"text-secondary\">.text-secondary</p>\n                    <p className=\"text-dark\">.text-dark</p>\n                    <p className=\"text-muted\">.text-muted</p>\n                    <p className=\"text-white bg-dark pl-1\">.text-white</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Top aligned media</h4>\n                <div className=\"media\">\n                  <i className=\"ti-world icon-md text-info d-flex align-self-start me-3\"></i>\n                  <div className=\"media-body\">\n                    <p className=\"card-text\">Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Center aligned media</h4>\n                <div className=\"media\">\n                  <i className=\"ti-world icon-md text-info d-flex align-self-center me-3\"></i>\n                  <div className=\"media-body\">\n                    <p className=\"card-text\">Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Bottom aligned media</h4>\n                <div className=\"media\">\n                  <i className=\"ti-world icon-md text-info d-flex align-self-end me-3\"></i>\n                  <div className=\"media-body\">\n                    <p className=\"card-text\">Cras sit amet nibh libero, in gravida nulla. Nulla vel metus scelerisque.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Highlighted Text</h4>\n                <p className=\"card-description\"> Wrap the text in <code>&lt;mark&gt;</code> to highlight text </p>\n                <p> It is a long <mark className=\"bg-warning text-white\">established</mark> fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution </p>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">List Unordered</h4>\n                <ul>\n                  <li>Lorem ipsum dolor sit amet</li>\n                  <li>Consectetur adipiscing elit</li>\n                  <li>Integer molestie lorem at massa</li>\n                  <li>Facilisis in pretium nisl aliquet</li>\n                  <li>Nulla volutpat aliquam velit</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Bold text</h4>\n                <p className=\"card-description\"> Use class<code>.font-weight-bold</code>\n                </p>\n                <p> It is a long <span className=\"font-weight-bold\">established fact</span> that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution </p>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">List Ordered</h4>\n                <ol>\n                  <li>Lorem ipsum dolor sit amet</li>\n                  <li>Consectetur adipiscing elit</li>\n                  <li>Integer molestie lorem at massa</li>\n                  <li>Facilisis in pretium nisl aliquet</li>\n                  <li>Nulla volutpat aliquam velit></li>\n                </ol>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-primary\">Underline</h4>\n                <p className=\"card-description\"> Wrap in <code>&lt;u&gt;</code> tag for underline </p>\n                <p>\n                  <u>lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua.</u>\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-danger\">Lowercase</h4>\n                <p className=\"card-description\"> Use class <code>.text-lowercase</code>\n                </p>\n                <p className=\"text-lowercase\"> lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. </p>\n              </div>\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-warning\">Uppercase</h4>\n                <p className=\"card-description\"> Use class <code>.text-uppercase</code>\n                </p>\n                <p className=\"text-uppercase\"> lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. </p>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-6 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">Mute</h4>\n                <p className=\"card-description\"> Use class <code>.text-muted</code>\n                </p>\n                <p className=\"text-muted\"> lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. </p>\n              </div>\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-success\">Strike</h4>\n                <p className=\"card-description\"> Wrap content in <code>&lt;del&gt;</code> tag </p>\n                <p>\n                  <del> lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. </del>\n                </p>\n              </div>\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-info\">Capitalized</h4>\n                <p className=\"card-description\"> Use class <code>.text-capitalize</code>\n                </p>\n                <p className=\"text-capitalize\"> lorem ipsum dolor sit amet, consectetur mod tempor incididunt ut labore et dolore magna aliqua. </p>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">List with icon</h4>\n                <p className=\"card-description\">Add class <code>.list-ticked</code> to <code>&lt;ul&gt;</code></p>\n                <ul className=\"list-ticked\">\n                  <li>Lorem ipsum dolor sit amet</li>\n                  <li>Consectetur adipiscing elit</li>\n                  <li>Integer molestie lorem at massa</li>\n                  <li>Facilisis in pretium nisl aliquet</li>\n                  <li>Nulla volutpat aliquam velit></li>\n                </ul>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">List with icon</h4>\n                <p className=\"card-description\">Add class <code>.list-arrow</code> to <code>&lt;ul&gt;</code></p>\n                <ul className=\"list-arrow\">\n                  <li>Lorem ipsum dolor sit amet</li>\n                  <li>Consectetur adipiscing elit</li>\n                  <li>Integer molestie lorem at massa</li>\n                  <li>Facilisis in pretium nisl aliquet</li>\n                  <li>Nulla volutpat aliquam velit></li>\n                </ul>\n              </div>\n            </div>\n          </div>\n          <div className=\"col-md-4 grid-margin stretch-card\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">List with icon</h4>\n                <p className=\"card-description\">Add class <code>.list-star</code> to <code>&lt;ul&gt;</code></p>\n                <ul className=\"list-star\">\n                  <li>Lorem ipsum dolor sit amet</li>\n                  <li>Consectetur adipiscing elit</li>\n                  <li>Integer molestie lorem at massa</li>\n                  <li>Facilisis in pretium nisl aliquet</li>\n                  <li>Nulla volutpat aliquam velit></li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n}\n\nexport default Typography\n"], "names": ["Typography", "className", "title", "Component"], "sourceRoot": ""}