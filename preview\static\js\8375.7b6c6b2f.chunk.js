"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[8375],{88375:function(s,a,e){e.r(a),e.d(a,{Paginations:function(){return w},default:function(){return A}});var c=e(15671),d=e(43144),i=e(60136),r=e(54062),l=e(72791),n=e(1413),t=e(45987),x=e(81694),o=e.n(x),h=e(10162),m=e(16445),j=e(80184),N=["active","disabled","className","style","activeLabel","children"],v=["children"],p=l.forwardRef((function(s,a){var e=s.active,c=s.disabled,d=s.className,i=s.style,r=s.activeLabel,l=s.children,x=(0,t.Z)(s,N),h=e||c?"span":m.Z;return(0,j.jsx)("li",{ref:a,style:i,className:o()(d,"page-item",{active:e,disabled:c}),children:(0,j.jsxs)(h,(0,n.Z)((0,n.Z)({className:"page-link",disabled:c},x),{},{children:[l,e&&r&&(0,j.jsx)("span",{className:"visually-hidden",children:r})]}))})}));p.defaultProps={active:!1,disabled:!1,activeLabel:"(current)"},p.displayName="PageItem";var u=p;function g(s,a){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s,c=l.forwardRef((function(s,c){var d=s.children,i=(0,t.Z)(s,v);return(0,j.jsxs)(p,(0,n.Z)((0,n.Z)({},i),{},{ref:c,children:[(0,j.jsx)("span",{"aria-hidden":"true",children:d||a}),(0,j.jsx)("span",{className:"visually-hidden",children:e})]}))}));return c.displayName=s,c}var f=g("First","\xab"),P=g("Prev","\u2039","Previous"),y=g("Ellipsis","\u2026","More"),b=g("Next","\u203a"),Z=g("Last","\xbb"),R=["bsPrefix","className","size"],L=l.forwardRef((function(s,a){var e=s.bsPrefix,c=s.className,d=s.size,i=(0,t.Z)(s,R),r=(0,h.vE)(e,"pagination");return(0,j.jsx)("ul",(0,n.Z)((0,n.Z)({ref:a},i),{},{className:o()(c,r,d&&"".concat(r,"-").concat(d))}))}));L.displayName="Pagination";var k=Object.assign(L,{First:f,Prev:P,Ellipsis:y,Item:u,Next:b,Last:Z}),w=function(s){(0,i.Z)(e,s);var a=(0,r.Z)(e);function e(){return(0,c.Z)(this,e),a.apply(this,arguments)}return(0,d.Z)(e,[{key:"render",value:function(){for(var s=[],a=1;a<=4;a++)s.push((0,j.jsx)(k.Item,{active:2===a,children:a},a));return(0,j.jsx)("div",{children:(0,j.jsxs)("div",{className:"row",children:[(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Basic Paginations"}),(0,j.jsx)("p",{className:"card-description",children:"Default bordered pagination"}),(0,j.jsxs)(k,{children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Flat Pagination"}),(0,j.jsxs)("p",{className:"card-description",children:["Add class ",(0,j.jsx)("code",{children:".flat"})]}),(0,j.jsxs)(k,{className:"flat pagination-success",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Separated  Pagination"}),(0,j.jsxs)("p",{className:"card-description",children:["Add class ",(0,j.jsx)("code",{children:".separated"})]}),(0,j.jsxs)(k,{className:"separated pagination-danger",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Bordered Rounded"}),(0,j.jsxs)("p",{className:"card-description",children:["Add class ",(0,j.jsx)("code",{children:".rounded"})]}),(0,j.jsxs)(k,{className:"rounded pagination-primary",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Flat Rounded Pagination"}),(0,j.jsxs)("p",{className:"card-description",children:["Add class ",(0,j.jsx)("code",{children:".rounded-flat"})]}),(0,j.jsxs)(k,{className:"rounded-flat pagination-success",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Separated Rounded"}),(0,j.jsxs)("p",{className:"card-description",children:["Add class ",(0,j.jsx)("code",{children:".rounded-separated"})]}),(0,j.jsxs)(k,{className:"rounded-separated pagination-danger",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Left Position"}),(0,j.jsx)("p",{className:"card-description",children:"Left Pagination"}),(0,j.jsxs)(k,{className:"pagination-primary d-flex justify-content-start",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Center Position"}),(0,j.jsx)("p",{className:"card-description",children:"Centered Pagination"}),(0,j.jsxs)(k,{className:"pagination-success d-flex justify-content-center",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})}),(0,j.jsx)("div",{className:"col-md-6 col-xl-4 grid-margin stretch-card",children:(0,j.jsx)("div",{className:"card",children:(0,j.jsxs)("div",{className:"card-body",children:[(0,j.jsx)("h4",{className:"card-title",children:"Right Position"}),(0,j.jsx)("p",{className:"card-description",children:"Right Pagination"}),(0,j.jsxs)(k,{className:"pagination-danger d-flex justify-content-end",children:[(0,j.jsx)(k.Prev,{}),s,(0,j.jsx)(k.Next,{})]})]})})})]})})}}]),e}(l.Component),A=w}}]);
//# sourceMappingURL=8375.7b6c6b2f.chunk.js.map