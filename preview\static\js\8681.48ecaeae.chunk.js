"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[8681],{38681:function(n,e,a){a.r(e),a.d(e,{CodeEditor:function(){return h}});var i=a(15671),l=a(43144),t=a(60136),o=a(54062),s=a(72791),r=a(38399),d=(a(65421),a(49166),a(88190),a(83004),a(79788),a(88704),a(88051),a(80184)),c={minHeight:"300px"},h=function(n){(0,t.Z)(a,n);var e=(0,o.Z)(a);function a(){return(0,i.Z)(this,a),e.apply(this,arguments)}return(0,l.Z)(a,[{key:"render",value:function(){return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"row",children:(0,d.jsx)("div",{className:"col-12 grid-margin",children:(0,d.jsx)("div",{className:"card",children:(0,d.jsxs)("div",{className:"card-body",children:[(0,d.jsx)("h4",{className:"card-title",children:"Ace Editor"}),(0,d.jsxs)("div",{className:"row",children:[(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"HTML Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"html",theme:"monokai",name:"firstEditor",onChange:this.onChange,editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:'\n<div className="panel panel-default">\n  <div className="panel-heading">\n    <h5 className="panel-title">\n      Panel Title\n      <span className="text-semibold">Default</span>\n      <small>Full featured toolbar</small>\n    </h5>\n    <ul className="panel-heading-icons">\n      <li>\n        <a href="!#" onClick={event => event.preventDefault()} data-panel="collapse"><i className="icon-arrow-down2"></i></a>\n      </li>\n      <li>\n        <a href="!#" onClick={event => event.preventDefault()} data-panel="reload"><i className="icon-reload"></i></a>\n      </li>\n      <li>\n        <a href="!#" onClick={event => event.preventDefault()} data-panel="move"><i className="icon-move"></i></a>\n      </li>\n      <li>\n        <a href="!#" onClick={event => event.preventDefault()} data-panel="close"><i className="icon-close"></i></a>\n      </li>\n    </ul>\n  </div>\n  <div className="panel-body">\n    Panel body\n  </div>\n</div>'})]}),(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"Javascript Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"java",theme:"monokai",name:"javaeditorEditor",editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:'\n/**\n  * In fact, you\'re looking at ACE right now. Go ahead and play with it!\n  *\n  * We are currently showing off the JavaScript mode. ACE has support for 45\n  * language modes and 24 color themes!\n*/\n    \n    function add(x, y) {\n      var resultString = "Hello, ACE! The result of your math is: ";\n      var result = x + y;\n      return resultString + result;\n    }\n    \n    var addResult = add(3, 2);\n    console.log(addResult);'})]})]}),(0,d.jsxs)("div",{className:"row",children:[(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"CSS Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"css",theme:"monokai",name:"cssEditor",editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:"\n  .nav ul {\n    margin: 0;\n    padding: 0;\n    list-style: none;\n  }\n  \n  .nav li {\n    display: inline-block;\n  }\n  \n  .nav a {\n    display: block;\n    padding: 6px 12px;\n    text-decoration: none;\n  }\n                      "})]}),(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"scss Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"sass",theme:"monokai",name:"sassEditor",editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:"\n  .nav {\n    ul {\n      margin: 0;\n      padding: 0;\n      list-style: none;\n    }\n  \n    li {\n      display: inline-block;\n    }\n  \n    a {\n      display: block;\n      padding: 6px 12px;\n      text-decoration: none;\n    }\n  }"})]})]}),(0,d.jsxs)("div",{className:"row",children:[(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"Json Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"json",theme:"monokai",name:"jsonEditor",editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:'\n  {\n    "firstName": "John",\n    "lastName": "Smith",\n    "isAlive": true,\n    "age": 27,\n    "address": {\n    "streetAddress": "21 2nd Street",\n    "city": "New York",\n    "state": "NY",\n    "postalCode": "10021-3100"\n    },\n    "phoneNumbers": [\n      {\n        "type": "home",\n        "number": "************"\n      },\n      {\n        "type": "office",\n        "number": "************"\n      },\n      {\n        "type": "mobile",\n        "number": "************"\n      }\n    ],\n    "children": [],\n    "spouse": null\n  }\n                      '})]}),(0,d.jsxs)("div",{className:"col-md-6 grid-margin",children:[(0,d.jsx)("h5",{className:"card-subtitle",children:"PHP Mode"}),(0,d.jsx)(r.ZP,{style:c,mode:"php",theme:"monokai",name:"phpEditor",editorProps:{$blockScrolling:!0},placeholder:"Placeholder Text",onLoad:this.onLoad,fontSize:14,showPrintMargin:!0,showGutter:!0,highlightActiveLine:!0,height:"100%",width:"100%",value:'\n  <?php\n\n  function nfact($n) {\n    if ($n == 0) {\n      return 1;\n    }\n    else {\n      return $n * nfact($n - 1);\n    }\n  }\n\n  echo "\n\nPlease enter a whole number ... ";\n  $num = trim(fgets(STDIN));\n\n\n  // ===== PROCESS - Determing the factorial of the input number =====\n\n  $output = "\n\nFactorial " . $num . " = " . nfact($num) . "\n\n";\n  echo $output;\n\n?>\n'})]})]})]})})})})})}}]),a}(s.Component);e.default=h}}]);
//# sourceMappingURL=8681.48ecaeae.chunk.js.map