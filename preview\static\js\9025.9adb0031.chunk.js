/*! For license information please see 9025.9adb0031.chunk.js.LICENSE.txt */
(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[9025],{69958:function(t,n,e){"use strict";e.r(n),e.d(n,{geoAlbers:function(){return kr},geoAlbersUsa:function(){return Nr},geoArea:function(){return ut},geoAzimuthalEqualArea:function(){return Zr},geoAzimuthalEqualAreaRaw:function(){return Ar},geoAzimuthalEquidistant:function(){return Or},geoAzimuthalEquidistantRaw:function(){return zr},geoBounds:function(){return Dt},geoCentroid:function(){return Qt},geoCircle:function(){return sn},geoClipAntimeridian:function(){return Nn},geoClipCircle:function(){return jn},geoClipExtent:function(){return Ln},geoClipRectangle:function(){return Tn},geoConicConformal:function(){return Gr},geoConicConformalRaw:function(){return qr},geoConicEqualArea:function(){return Sr},geoConicEqualAreaRaw:function(){return Mr},geoConicEquidistant:function(){return Xr},geoConicEquidistantRaw:function(){return Ir},geoContains:function(){return Qn},geoDistance:function(){return Fn},geoEqualEarth:function(){return Kr},geoEqualEarthRaw:function(){return Wr},geoEquirectangular:function(){return Dr},geoEquirectangularRaw:function(){return Yr},geoGnomonic:function(){return Jr},geoGnomonicRaw:function(){return $r},geoGraticule:function(){return re},geoGraticule10:function(){return oe},geoIdentity:function(){return Qr},geoInterpolate:function(){return ie},geoLength:function(){return Dn},geoMercator:function(){return Tr},geoMercatorRaw:function(){return Cr},geoNaturalEarth1:function(){return no},geoNaturalEarth1Raw:function(){return to},geoOrthographic:function(){return ro},geoOrthographicRaw:function(){return eo},geoPath:function(){return cr},geoProjection:function(){return br},geoProjectionMutator:function(){return Er},geoRotation:function(){return cn},geoStereographic:function(){return io},geoStereographicRaw:function(){return oo},geoStream:function(){return L},geoTransform:function(){return lr},geoTransverseMercator:function(){return ao},geoTransverseMercatorRaw:function(){return uo}});var r=e(15671),o=e(43144),i=function(){function t(){(0,r.Z)(this,t),this._partials=new Float64Array(32),this._n=0}return(0,o.Z)(t,[{key:"add",value:function(t){for(var n=this._partials,e=0,r=0;r<this._n&&r<32;r++){var o=n[r],i=t+o,u=Math.abs(t)<Math.abs(o)?t-(i-o):o-(i-t);u&&(n[e++]=u),t=i}return n[e]=t,this._n=e+1,this}},{key:"valueOf",value:function(){var t,n,e,r=this._partials,o=this._n,i=0;if(o>0){for(i=r[--o];o>0&&(t=i,!(e=(n=r[--o])-((i=t+n)-t))););o>0&&(e<0&&r[o-1]<0||e>0&&r[o-1]>0)&&(n=2*e)==(t=i+n)-i&&(i=t)}return i}}]),t}();var u=1e-6,a=1e-12,c=Math.PI,l=c/2,f=c/4,s=2*c,h=180/c,p=c/180,d=Math.abs,v=Math.atan,g=Math.atan2,m=Math.cos,y=Math.ceil,w=Math.exp,_=(Math.floor,Math.hypot),b=Math.log,E=Math.pow,x=Math.sin,M=Math.sign||function(t){return t>0?1:t<0?-1:0},S=Math.sqrt,k=Math.tan;function N(t){return t>1?0:t<-1?c:Math.acos(t)}function j(t){return t>1?l:t<-1?-l:Math.asin(t)}function P(t){return(t=x(t/2))*t}function A(){}function Z(t,n){t&&O.hasOwnProperty(t.type)&&O[t.type](t,n)}var z={Feature:function(t,n){Z(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,o=e.length;++r<o;)Z(e[r].geometry,n)}},O={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){C(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)C(e[r],n,0)},Polygon:function(t,n){T(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)T(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,o=e.length;++r<o;)Z(e[r],n)}};function C(t,n,e){var r,o=-1,i=t.length-e;for(n.lineStart();++o<i;)r=t[o],n.point(r[0],r[1],r[2]);n.lineEnd()}function T(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)C(t[e],n,1);n.polygonEnd()}function L(t,n){t&&z.hasOwnProperty(t.type)?z[t.type](t,n):Z(t,n)}var R,q,G,Y,D,I,X,F,B,U,H,V,W,K,$,J,Q=new i,tt=new i,nt={point:A,lineStart:A,lineEnd:A,polygonStart:function(){Q=new i,nt.lineStart=et,nt.lineEnd=rt},polygonEnd:function(){var t=+Q;tt.add(t<0?s+t:t),this.lineStart=this.lineEnd=this.point=A},sphere:function(){tt.add(s)}};function et(){nt.point=ot}function rt(){it(R,q)}function ot(t,n){nt.point=it,R=t,q=n,G=t*=p,Y=m(n=(n*=p)/2+f),D=x(n)}function it(t,n){var e=(t*=p)-G,r=e>=0?1:-1,o=r*e,i=m(n=(n*=p)/2+f),u=x(n),a=D*u,c=Y*i+a*m(o),l=a*r*x(o);Q.add(g(l,c)),G=t,Y=i,D=u}function ut(t){return tt=new i,L(t,nt),2*tt}function at(t){return[g(t[1],t[0]),j(t[2])]}function ct(t){var n=t[0],e=t[1],r=m(e);return[r*m(n),r*x(n),x(e)]}function lt(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function ft(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function st(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function ht(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function pt(t){var n=S(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var dt,vt,gt,mt,yt,wt,_t,bt,Et,xt,Mt,St,kt,Nt,jt,Pt,At={point:Zt,lineStart:Ot,lineEnd:Ct,polygonStart:function(){At.point=Tt,At.lineStart=Lt,At.lineEnd=Rt,K=new i,nt.polygonStart()},polygonEnd:function(){nt.polygonEnd(),At.point=Zt,At.lineStart=Ot,At.lineEnd=Ct,Q<0?(I=-(F=180),X=-(B=90)):K>u?B=90:K<-u&&(X=-90),J[0]=I,J[1]=F},sphere:function(){I=-(F=180),X=-(B=90)}};function Zt(t,n){$.push(J=[I=t,F=t]),n<X&&(X=n),n>B&&(B=n)}function zt(t,n){var e=ct([t*p,n*p]);if(W){var r=ft(W,e),o=ft([r[1],-r[0],0],r);pt(o),o=at(o);var i,u=t-U,a=u>0?1:-1,c=o[0]*h*a,l=d(u)>180;l^(a*U<c&&c<a*t)?(i=o[1]*h)>B&&(B=i):l^(a*U<(c=(c+360)%360-180)&&c<a*t)?(i=-o[1]*h)<X&&(X=i):(n<X&&(X=n),n>B&&(B=n)),l?t<U?qt(I,t)>qt(I,F)&&(F=t):qt(t,F)>qt(I,F)&&(I=t):F>=I?(t<I&&(I=t),t>F&&(F=t)):t>U?qt(I,t)>qt(I,F)&&(F=t):qt(t,F)>qt(I,F)&&(I=t)}else $.push(J=[I=t,F=t]);n<X&&(X=n),n>B&&(B=n),W=e,U=t}function Ot(){At.point=zt}function Ct(){J[0]=I,J[1]=F,At.point=Zt,W=null}function Tt(t,n){if(W){var e=t-U;K.add(d(e)>180?e+(e>0?360:-360):e)}else H=t,V=n;nt.point(t,n),zt(t,n)}function Lt(){nt.lineStart()}function Rt(){Tt(H,V),nt.lineEnd(),d(K)>u&&(I=-(F=180)),J[0]=I,J[1]=F,W=null}function qt(t,n){return(n-=t)<0?n+360:n}function Gt(t,n){return t[0]-n[0]}function Yt(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function Dt(t){var n,e,r,o,i,u,a;if(B=F=-(I=X=1/0),$=[],L(t,At),e=$.length){for($.sort(Gt),n=1,i=[r=$[0]];n<e;++n)Yt(r,(o=$[n])[0])||Yt(r,o[1])?(qt(r[0],o[1])>qt(r[0],r[1])&&(r[1]=o[1]),qt(o[0],r[1])>qt(r[0],r[1])&&(r[0]=o[0])):i.push(r=o);for(u=-1/0,n=0,r=i[e=i.length-1];n<=e;r=o,++n)o=i[n],(a=qt(r[1],o[0]))>u&&(u=a,I=o[0],F=r[1])}return $=J=null,I===1/0||X===1/0?[[NaN,NaN],[NaN,NaN]]:[[I,X],[F,B]]}var It={sphere:A,point:Xt,lineStart:Bt,lineEnd:Vt,polygonStart:function(){It.lineStart=Wt,It.lineEnd=Kt},polygonEnd:function(){It.lineStart=Bt,It.lineEnd=Vt}};function Xt(t,n){t*=p;var e=m(n*=p);Ft(e*m(t),e*x(t),x(n))}function Ft(t,n,e){++dt,gt+=(t-gt)/dt,mt+=(n-mt)/dt,yt+=(e-yt)/dt}function Bt(){It.point=Ut}function Ut(t,n){t*=p;var e=m(n*=p);Nt=e*m(t),jt=e*x(t),Pt=x(n),It.point=Ht,Ft(Nt,jt,Pt)}function Ht(t,n){t*=p;var e=m(n*=p),r=e*m(t),o=e*x(t),i=x(n),u=g(S((u=jt*i-Pt*o)*u+(u=Pt*r-Nt*i)*u+(u=Nt*o-jt*r)*u),Nt*r+jt*o+Pt*i);vt+=u,wt+=u*(Nt+(Nt=r)),_t+=u*(jt+(jt=o)),bt+=u*(Pt+(Pt=i)),Ft(Nt,jt,Pt)}function Vt(){It.point=Xt}function Wt(){It.point=$t}function Kt(){Jt(St,kt),It.point=Xt}function $t(t,n){St=t,kt=n,t*=p,n*=p,It.point=Jt;var e=m(n);Nt=e*m(t),jt=e*x(t),Pt=x(n),Ft(Nt,jt,Pt)}function Jt(t,n){t*=p;var e=m(n*=p),r=e*m(t),o=e*x(t),i=x(n),u=jt*i-Pt*o,a=Pt*r-Nt*i,c=Nt*o-jt*r,l=_(u,a,c),f=j(l),s=l&&-f/l;Et.add(s*u),xt.add(s*a),Mt.add(s*c),vt+=f,wt+=f*(Nt+(Nt=r)),_t+=f*(jt+(jt=o)),bt+=f*(Pt+(Pt=i)),Ft(Nt,jt,Pt)}function Qt(t){dt=vt=gt=mt=yt=wt=_t=bt=0,Et=new i,xt=new i,Mt=new i,L(t,It);var n=+Et,e=+xt,r=+Mt,o=_(n,e,r);return o<a&&(n=wt,e=_t,r=bt,vt<u&&(n=gt,e=mt,r=yt),(o=_(n,e,r))<a)?[NaN,NaN]:[g(e,n)*h,j(r/o)*h]}function tn(t){return function(){return t}}function nn(t,n){function e(e,r){return e=t(e,r),n(e[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function en(t,n){return[d(t)>c?t+Math.round(-t/s)*s:t,n]}function rn(t,n,e){return(t%=s)?n||e?nn(un(t),an(n,e)):un(t):n||e?an(n,e):en}function on(t){return function(n,e){return[(n+=t)>c?n-s:n<-c?n+s:n,e]}}function un(t){var n=on(t);return n.invert=on(-t),n}function an(t,n){var e=m(t),r=x(t),o=m(n),i=x(n);function u(t,n){var u=m(n),a=m(t)*u,c=x(t)*u,l=x(n),f=l*e+a*r;return[g(c*o-f*i,a*e-l*r),j(f*o+c*i)]}return u.invert=function(t,n){var u=m(n),a=m(t)*u,c=x(t)*u,l=x(n),f=l*o-c*i;return[g(c*o+l*i,a*e+f*r),j(f*e-a*r)]},u}function cn(t){function n(n){return(n=t(n[0]*p,n[1]*p))[0]*=h,n[1]*=h,n}return t=rn(t[0]*p,t[1]*p,t.length>2?t[2]*p:0),n.invert=function(n){return(n=t.invert(n[0]*p,n[1]*p))[0]*=h,n[1]*=h,n},n}function ln(t,n,e,r,o,i){if(e){var u=m(n),a=x(n),c=r*e;null==o?(o=n+r*s,i=n-c/2):(o=fn(u,o),i=fn(u,i),(r>0?o<i:o>i)&&(o+=r*s));for(var l,f=o;r>0?f>i:f<i;f-=c)l=at([u,-a*m(f),-a*x(f)]),t.point(l[0],l[1])}}function fn(t,n){(n=ct(n))[0]-=t,pt(n);var e=N(-n[1]);return((-n[2]<0?-e:e)+s-u)%s}function sn(){var t,n,e=tn([0,0]),r=tn(90),o=tn(6),i={point:function(e,r){t.push(e=n(e,r)),e[0]*=h,e[1]*=h}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*p,c=o.apply(this,arguments)*p;return t=[],n=rn(-u[0]*p,-u[1]*p,0).invert,ln(i,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"===typeof t?t:tn([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"===typeof t?t:tn(+t),u):r},u.precision=function(t){return arguments.length?(o="function"===typeof t?t:tn(+t),u):o},u}function hn(){var t,n=[];return{point:function(n,e,r){t.push([n,e,r])},lineStart:function(){n.push(t=[])},lineEnd:A,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function pn(t,n){return d(t[0]-n[0])<u&&d(t[1]-n[1])<u}function dn(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function vn(t,n,e,r,o){var i,u,a=[],c=[];if(t.forEach((function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(pn(r,u)){if(!r[2]&&!u[2]){for(o.lineStart(),i=0;i<n;++i)o.point((r=t[i])[0],r[1]);return void o.lineEnd()}u[0]+=2e-6}a.push(e=new dn(r,t,null,!0)),c.push(e.o=new dn(r,null,e,!1)),a.push(e=new dn(u,t,null,!1)),c.push(e.o=new dn(u,null,e,!0))}})),a.length){for(c.sort(n),gn(a),gn(c),i=0,u=c.length;i<u;++i)c[i].e=e=!e;for(var l,f,s=a[0];;){for(var h=s,p=!0;h.v;)if((h=h.n)===s)return;l=h.z,o.lineStart();do{if(h.v=h.o.v=!0,h.e){if(p)for(i=0,u=l.length;i<u;++i)o.point((f=l[i])[0],f[1]);else r(h.x,h.n.x,1,o);h=h.n}else{if(p)for(l=h.p.z,i=l.length-1;i>=0;--i)o.point((f=l[i])[0],f[1]);else r(h.x,h.p.x,-1,o);h=h.p}l=(h=h.o).z,p=!p}while(!h.v);o.lineEnd()}}}function gn(t){if(n=t.length){for(var n,e,r=0,o=t[0];++r<n;)o.n=e=t[r],e.p=o,o=e;o.n=e=t[0],e.p=o}}function mn(t){return d(t[0])<=c?t[0]:M(t[0])*((d(t[0])+c)%s-c)}function yn(t,n){var e=mn(n),r=n[1],o=x(r),a=[x(e),-m(e),0],h=0,p=0,d=new i;1===o?r=l+u:-1===o&&(r=-l-u);for(var v=0,y=t.length;v<y;++v)if(_=(w=t[v]).length)for(var w,_,b=w[_-1],E=mn(b),M=b[1]/2+f,S=x(M),k=m(M),N=0;N<_;++N,E=A,S=z,k=O,b=P){var P=w[N],A=mn(P),Z=P[1]/2+f,z=x(Z),O=m(Z),C=A-E,T=C>=0?1:-1,L=T*C,R=L>c,q=S*z;if(d.add(g(q*T*x(L),k*O+q*m(L))),h+=R?C+T*s:C,R^E>=e^A>=e){var G=ft(ct(b),ct(P));pt(G);var Y=ft(a,G);pt(Y);var D=(R^C>=0?-1:1)*j(Y[2]);(r>D||r===D&&(G[0]||G[1]))&&(p+=R^C>=0?1:-1)}}return(h<-u||h<u&&d<-1e-12)^1&p}en.invert=en;var wn=e(74165),_n=e(37762),bn=(0,wn.Z)().mark(En);function En(t){var n,e,r;return(0,wn.Z)().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:n=(0,_n.Z)(t),o.prev=1,n.s();case 3:if((e=n.n()).done){o.next=8;break}return r=e.value,o.delegateYield(r,"t0",6);case 6:o.next=3;break;case 8:o.next=13;break;case 10:o.prev=10,o.t1=o.catch(1),n.e(o.t1);case 13:return o.prev=13,n.f(),o.finish(13);case 16:case"end":return o.stop()}}),bn,null,[[1,10,13,16]])}function xn(t){return Array.from(En(t))}function Mn(t,n,e,r){return function(o){var i,u,a,c=n(o),l=hn(),f=n(l),s=!1,h={point:p,lineStart:v,lineEnd:g,polygonStart:function(){h.point=m,h.lineStart=y,h.lineEnd=w,u=[],i=[]},polygonEnd:function(){h.point=p,h.lineStart=v,h.lineEnd=g,u=xn(u);var t=yn(i,r);u.length?(s||(o.polygonStart(),s=!0),vn(u,kn,t,e,o)):t&&(s||(o.polygonStart(),s=!0),o.lineStart(),e(null,null,1,o),o.lineEnd()),s&&(o.polygonEnd(),s=!1),u=i=null},sphere:function(){o.polygonStart(),o.lineStart(),e(null,null,1,o),o.lineEnd(),o.polygonEnd()}};function p(n,e){t(n,e)&&o.point(n,e)}function d(t,n){c.point(t,n)}function v(){h.point=d,c.lineStart()}function g(){h.point=p,c.lineEnd()}function m(t,n){a.push([t,n]),f.point(t,n)}function y(){f.lineStart(),a=[]}function w(){m(a[0][0],a[0][1]),f.lineEnd();var t,n,e,r,c=f.clean(),h=l.result(),p=h.length;if(a.pop(),i.push(a),a=null,p)if(1&c){if((n=(e=h[0]).length-1)>0){for(s||(o.polygonStart(),s=!0),o.lineStart(),t=0;t<n;++t)o.point((r=e[t])[0],r[1]);o.lineEnd()}}else p>1&&2&c&&h.push(h.pop().concat(h.shift())),u.push(h.filter(Sn))}return h}}function Sn(t){return t.length>1}function kn(t,n){return((t=t.x)[0]<0?t[1]-l-u:l-t[1])-((n=n.x)[0]<0?n[1]-l-u:l-n[1])}var Nn=Mn((function(){return!0}),(function(t){var n,e=NaN,r=NaN,o=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(i,a){var f=i>0?c:-c,s=d(i-e);d(s-c)<u?(t.point(e,r=(r+a)/2>0?l:-l),t.point(o,r),t.lineEnd(),t.lineStart(),t.point(f,r),t.point(i,r),n=0):o!==f&&s>=c&&(d(e-o)<u&&(e-=o*u),d(i-f)<u&&(i-=f*u),r=function(t,n,e,r){var o,i,a=x(t-e);return d(a)>u?v((x(n)*(i=m(r))*x(e)-x(r)*(o=m(n))*x(t))/(o*i*a)):(n+r)/2}(e,r,i,a),t.point(o,r),t.lineEnd(),t.lineStart(),t.point(f,r),n=0),t.point(e=i,r=a),o=f},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}}),(function(t,n,e,r){var o;if(null==t)o=e*l,r.point(-c,o),r.point(0,o),r.point(c,o),r.point(c,0),r.point(c,-o),r.point(0,-o),r.point(-c,-o),r.point(-c,0),r.point(-c,o);else if(d(t[0]-n[0])>u){var i=t[0]<n[0]?c:-c;o=e*i/2,r.point(-i,o),r.point(0,o),r.point(i,o)}else r.point(n[0],n[1])}),[-c,-l]);function jn(t){var n=m(t),e=6*p,r=n>0,o=d(n)>u;function i(t,e){return m(t)*m(e)>n}function a(t,e,r){var o=[1,0,0],i=ft(ct(t),ct(e)),a=lt(i,i),l=i[0],f=a-l*l;if(!f)return!r&&t;var s=n*a/f,h=-n*l/f,p=ft(o,i),v=ht(o,s);st(v,ht(i,h));var g=p,m=lt(v,g),y=lt(g,g),w=m*m-y*(lt(v,v)-1);if(!(w<0)){var _=S(w),b=ht(g,(-m-_)/y);if(st(b,v),b=at(b),!r)return b;var E,x=t[0],M=e[0],k=t[1],N=e[1];M<x&&(E=x,x=M,M=E);var j=M-x,P=d(j-c)<u;if(!P&&N<k&&(E=k,k=N,N=E),P||j<u?P?k+N>0^b[1]<(d(b[0]-x)<u?k:N):k<=b[1]&&b[1]<=N:j>c^(x<=b[0]&&b[0]<=M)){var A=ht(g,(-m+_)/y);return st(A,v),[b,at(A)]}}}function l(n,e){var o=r?t:c-t,i=0;return n<-o?i|=1:n>o&&(i|=2),e<-o?i|=4:e>o&&(i|=8),i}return Mn(i,(function(t){var n,e,u,f,s;return{lineStart:function(){f=u=!1,s=1},point:function(h,p){var d,v=[h,p],g=i(h,p),m=r?g?0:l(h,p):g?l(h+(h<0?c:-c),p):0;if(!n&&(f=u=g)&&t.lineStart(),g!==u&&(!(d=a(n,v))||pn(n,d)||pn(v,d))&&(v[2]=1),g!==u)s=0,g?(t.lineStart(),d=a(v,n),t.point(d[0],d[1])):(d=a(n,v),t.point(d[0],d[1],2),t.lineEnd()),n=d;else if(o&&n&&r^g){var y;m&e||!(y=a(v,n,!0))||(s=0,r?(t.lineStart(),t.point(y[0][0],y[0][1]),t.point(y[1][0],y[1][1]),t.lineEnd()):(t.point(y[1][0],y[1][1]),t.lineEnd(),t.lineStart(),t.point(y[0][0],y[0][1],3)))}!g||n&&pn(n,v)||t.point(v[0],v[1]),n=v,u=g,e=m},lineEnd:function(){u&&t.lineEnd(),n=null},clean:function(){return s|(f&&u)<<1}}}),(function(n,r,o,i){ln(i,t,e,o,n,r)}),r?[0,-t]:[-c,t-c])}var Pn,An,Zn,zn,On=1e9,Cn=-On;function Tn(t,n,e,r){function o(o,i){return t<=o&&o<=e&&n<=i&&i<=r}function i(o,i,u,c){var f=0,s=0;if(null==o||(f=a(o,u))!==(s=a(i,u))||l(o,i)<0^u>0)do{c.point(0===f||3===f?t:e,f>1?r:n)}while((f=(f+u+4)%4)!==s);else c.point(i[0],i[1])}function a(r,o){return d(r[0]-t)<u?o>0?0:3:d(r[0]-e)<u?o>0?2:1:d(r[1]-n)<u?o>0?1:0:o>0?3:2}function c(t,n){return l(t.x,n.x)}function l(t,n){var e=a(t,1),r=a(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var a,l,f,s,h,p,d,v,g,m,y,w=u,_=hn(),b={point:E,lineStart:function(){b.point=x,l&&l.push(f=[]);m=!0,g=!1,d=v=NaN},lineEnd:function(){a&&(x(s,h),p&&g&&_.rejoin(),a.push(_.result()));b.point=E,g&&w.lineEnd()},polygonStart:function(){w=_,a=[],l=[],y=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,o=l.length;e<o;++e)for(var i,u,a=l[e],c=1,f=a.length,s=a[0],h=s[0],p=s[1];c<f;++c)i=h,u=p,h=(s=a[c])[0],p=s[1],u<=r?p>r&&(h-i)*(r-u)>(p-u)*(t-i)&&++n:p<=r&&(h-i)*(r-u)<(p-u)*(t-i)&&--n;return n}(),e=y&&n,o=(a=xn(a)).length;(e||o)&&(u.polygonStart(),e&&(u.lineStart(),i(null,null,1,u),u.lineEnd()),o&&vn(a,c,n,i,u),u.polygonEnd());w=u,a=l=f=null}};function E(t,n){o(t,n)&&w.point(t,n)}function x(i,u){var a=o(i,u);if(l&&f.push([i,u]),m)s=i,h=u,p=a,m=!1,a&&(w.lineStart(),w.point(i,u));else if(a&&g)w.point(i,u);else{var c=[d=Math.max(Cn,Math.min(On,d)),v=Math.max(Cn,Math.min(On,v))],_=[i=Math.max(Cn,Math.min(On,i)),u=Math.max(Cn,Math.min(On,u))];!function(t,n,e,r,o,i){var u,a=t[0],c=t[1],l=0,f=1,s=n[0]-a,h=n[1]-c;if(u=e-a,s||!(u>0)){if(u/=s,s<0){if(u<l)return;u<f&&(f=u)}else if(s>0){if(u>f)return;u>l&&(l=u)}if(u=o-a,s||!(u<0)){if(u/=s,s<0){if(u>f)return;u>l&&(l=u)}else if(s>0){if(u<l)return;u<f&&(f=u)}if(u=r-c,h||!(u>0)){if(u/=h,h<0){if(u<l)return;u<f&&(f=u)}else if(h>0){if(u>f)return;u>l&&(l=u)}if(u=i-c,h||!(u<0)){if(u/=h,h<0){if(u>f)return;u>l&&(l=u)}else if(h>0){if(u<l)return;u<f&&(f=u)}return l>0&&(t[0]=a+l*s,t[1]=c+l*h),f<1&&(n[0]=a+f*s,n[1]=c+f*h),!0}}}}}(c,_,t,n,e,r)?a&&(w.lineStart(),w.point(i,u),y=!1):(g||(w.lineStart(),w.point(c[0],c[1])),w.point(_[0],_[1]),a||w.lineEnd(),y=!1)}d=i,v=u,g=a}return b}}function Ln(){var t,n,e,r=0,o=0,i=960,u=500;return e={stream:function(e){return t&&n===e?t:t=Tn(r,o,i,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],o=+a[0][1],i=+a[1][0],u=+a[1][1],t=n=null,e):[[r,o],[i,u]]}}}var Rn={sphere:A,point:A,lineStart:function(){Rn.point=Gn,Rn.lineEnd=qn},lineEnd:A,polygonStart:A,polygonEnd:A};function qn(){Rn.point=Rn.lineEnd=A}function Gn(t,n){An=t*=p,Zn=x(n*=p),zn=m(n),Rn.point=Yn}function Yn(t,n){t*=p;var e=x(n*=p),r=m(n),o=d(t-An),i=m(o),u=r*x(o),a=zn*e-Zn*r*i,c=Zn*e+zn*r*i;Pn.add(g(S(u*u+a*a),c)),An=t,Zn=e,zn=r}function Dn(t){return Pn=new i,L(t,Rn),+Pn}var In=[null,null],Xn={type:"LineString",coordinates:In};function Fn(t,n){return In[0]=t,In[1]=n,Dn(Xn)}var Bn={Feature:function(t,n){return Hn(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,o=e.length;++r<o;)if(Hn(e[r].geometry,n))return!0;return!1}},Un={Sphere:function(){return!0},Point:function(t,n){return Vn(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)if(Vn(e[r],n))return!0;return!1},LineString:function(t,n){return Wn(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)if(Wn(e[r],n))return!0;return!1},Polygon:function(t,n){return Kn(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,o=e.length;++r<o;)if(Kn(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,o=e.length;++r<o;)if(Hn(e[r],n))return!0;return!1}};function Hn(t,n){return!(!t||!Un.hasOwnProperty(t.type))&&Un[t.type](t,n)}function Vn(t,n){return 0===Fn(t,n)}function Wn(t,n){for(var e,r,o,i=0,u=t.length;i<u;i++){if(0===(r=Fn(t[i],n)))return!0;if(i>0&&(o=Fn(t[i],t[i-1]))>0&&e<=o&&r<=o&&(e+r-o)*(1-Math.pow((e-r)/o,2))<a*o)return!0;e=r}return!1}function Kn(t,n){return!!yn(t.map($n),Jn(n))}function $n(t){return(t=t.map(Jn)).pop(),t}function Jn(t){return[t[0]*p,t[1]*p]}function Qn(t,n){return(t&&Bn.hasOwnProperty(t.type)?Bn[t.type]:Hn)(t,n)}function te(t,n,e){t=+t,n=+n,e=(o=arguments.length)<2?(n=t,t=0,1):o<3?1:+e;for(var r=-1,o=0|Math.max(0,Math.ceil((n-t)/e)),i=new Array(o);++r<o;)i[r]=t+r*e;return i}function ne(t,n,e){var r=te(t,n-u,e).concat(n);return function(t){return r.map((function(n){return[t,n]}))}}function ee(t,n,e){var r=te(t,n-u,e).concat(n);return function(t){return r.map((function(n){return[n,t]}))}}function re(){var t,n,e,r,o,i,a,c,l,f,s,h,p=10,v=p,g=90,m=360,w=2.5;function _(){return{type:"MultiLineString",coordinates:b()}}function b(){return te(y(r/g)*g,e,g).map(s).concat(te(y(c/m)*m,a,m).map(h)).concat(te(y(n/p)*p,t,p).filter((function(t){return d(t%g)>u})).map(l)).concat(te(y(i/v)*v,o,v).filter((function(t){return d(t%m)>u})).map(f))}return _.lines=function(){return b().map((function(t){return{type:"LineString",coordinates:t}}))},_.outline=function(){return{type:"Polygon",coordinates:[s(r).concat(h(a).slice(1),s(e).reverse().slice(1),h(c).reverse().slice(1))]}},_.extent=function(t){return arguments.length?_.extentMajor(t).extentMinor(t):_.extentMinor()},_.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],c=+t[0][1],a=+t[1][1],r>e&&(t=r,r=e,e=t),c>a&&(t=c,c=a,a=t),_.precision(w)):[[r,c],[e,a]]},_.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],i=+e[0][1],o=+e[1][1],n>t&&(e=n,n=t,t=e),i>o&&(e=i,i=o,o=e),_.precision(w)):[[n,i],[t,o]]},_.step=function(t){return arguments.length?_.stepMajor(t).stepMinor(t):_.stepMinor()},_.stepMajor=function(t){return arguments.length?(g=+t[0],m=+t[1],_):[g,m]},_.stepMinor=function(t){return arguments.length?(p=+t[0],v=+t[1],_):[p,v]},_.precision=function(u){return arguments.length?(w=+u,l=ne(i,o,90),f=ee(n,t,w),s=ne(c,a,90),h=ee(r,e,w),_):w},_.extentMajor([[-180,-89.999999],[180,89.999999]]).extentMinor([[-180,-80.000001],[180,80.000001]])}function oe(){return re()()}function ie(t,n){var e=t[0]*p,r=t[1]*p,o=n[0]*p,i=n[1]*p,u=m(r),a=x(r),c=m(i),l=x(i),f=u*m(e),s=u*x(e),d=c*m(o),v=c*x(o),y=2*j(S(P(i-r)+u*c*P(o-e))),w=x(y),_=y?function(t){var n=x(t*=y)/w,e=x(y-t)/w,r=e*f+n*d,o=e*s+n*v,i=e*a+n*l;return[g(o,r)*h,g(i,S(r*r+o*o))*h]}:function(){return[e*h,r*h]};return _.distance=y,_}var ue,ae,ce,le,fe=function(t){return t},se=new i,he=new i,pe={point:A,lineStart:A,lineEnd:A,polygonStart:function(){pe.lineStart=de,pe.lineEnd=me},polygonEnd:function(){pe.lineStart=pe.lineEnd=pe.point=A,se.add(d(he)),he=new i},result:function(){var t=se/2;return se=new i,t}};function de(){pe.point=ve}function ve(t,n){pe.point=ge,ue=ce=t,ae=le=n}function ge(t,n){he.add(le*t-ce*n),ce=t,le=n}function me(){ge(ue,ae)}var ye=pe,we=1/0,_e=we,be=-we,Ee=be,xe={point:function(t,n){t<we&&(we=t);t>be&&(be=t);n<_e&&(_e=n);n>Ee&&(Ee=n)},lineStart:A,lineEnd:A,polygonStart:A,polygonEnd:A,result:function(){var t=[[we,_e],[be,Ee]];return be=Ee=-(_e=we=1/0),t}};var Me,Se,ke,Ne,je=xe,Pe=0,Ae=0,Ze=0,ze=0,Oe=0,Ce=0,Te=0,Le=0,Re=0,qe={point:Ge,lineStart:Ye,lineEnd:Xe,polygonStart:function(){qe.lineStart=Fe,qe.lineEnd=Be},polygonEnd:function(){qe.point=Ge,qe.lineStart=Ye,qe.lineEnd=Xe},result:function(){var t=Re?[Te/Re,Le/Re]:Ce?[ze/Ce,Oe/Ce]:Ze?[Pe/Ze,Ae/Ze]:[NaN,NaN];return Pe=Ae=Ze=ze=Oe=Ce=Te=Le=Re=0,t}};function Ge(t,n){Pe+=t,Ae+=n,++Ze}function Ye(){qe.point=De}function De(t,n){qe.point=Ie,Ge(ke=t,Ne=n)}function Ie(t,n){var e=t-ke,r=n-Ne,o=S(e*e+r*r);ze+=o*(ke+t)/2,Oe+=o*(Ne+n)/2,Ce+=o,Ge(ke=t,Ne=n)}function Xe(){qe.point=Ge}function Fe(){qe.point=Ue}function Be(){He(Me,Se)}function Ue(t,n){qe.point=He,Ge(Me=ke=t,Se=Ne=n)}function He(t,n){var e=t-ke,r=n-Ne,o=S(e*e+r*r);ze+=o*(ke+t)/2,Oe+=o*(Ne+n)/2,Ce+=o,Te+=(o=Ne*t-ke*n)*(ke+t),Le+=o*(Ne+n),Re+=3*o,Ge(ke=t,Ne=n)}var Ve=qe;function We(t){this._context=t}We.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,s)}},result:A};var Ke,$e,Je,Qe,tr,nr=new i,er={point:A,lineStart:function(){er.point=rr},lineEnd:function(){Ke&&or($e,Je),er.point=A},polygonStart:function(){Ke=!0},polygonEnd:function(){Ke=null},result:function(){var t=+nr;return nr=new i,t}};function rr(t,n){er.point=or,$e=Qe=t,Je=tr=n}function or(t,n){Qe-=t,tr-=n,nr.add(S(Qe*Qe+tr*tr)),Qe=t,tr=n}var ir=er;function ur(){this._string=[]}function ar(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}function cr(t,n){var e,r,o=4.5;function i(t){return t&&("function"===typeof o&&r.pointRadius(+o.apply(this,arguments)),L(t,e(r))),r.result()}return i.area=function(t){return L(t,e(ye)),ye.result()},i.measure=function(t){return L(t,e(ir)),ir.result()},i.bounds=function(t){return L(t,e(je)),je.result()},i.centroid=function(t){return L(t,e(Ve)),Ve.result()},i.projection=function(n){return arguments.length?(e=null==n?(t=null,fe):(t=n).stream,i):t},i.context=function(t){return arguments.length?(r=null==t?(n=null,new ur):new We(n=t),"function"!==typeof o&&r.pointRadius(o),i):n},i.pointRadius=function(t){return arguments.length?(o="function"===typeof t?t:(r.pointRadius(+t),+t),i):o},i.projection(t).context(n)}function lr(t){return{stream:fr(t)}}function fr(t){return function(n){var e=new sr;for(var r in t)e[r]=t[r];return e.stream=n,e}}function sr(){}function hr(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),L(e,t.stream(je)),n(je.result()),null!=r&&t.clipExtent(r),t}function pr(t,n,e){return hr(t,(function(e){var r=n[1][0]-n[0][0],o=n[1][1]-n[0][1],i=Math.min(r/(e[1][0]-e[0][0]),o/(e[1][1]-e[0][1])),u=+n[0][0]+(r-i*(e[1][0]+e[0][0]))/2,a=+n[0][1]+(o-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([u,a])}),e)}function dr(t,n,e){return pr(t,[[0,0],n],e)}function vr(t,n,e){return hr(t,(function(e){var r=+n,o=r/(e[1][0]-e[0][0]),i=(r-o*(e[1][0]+e[0][0]))/2,u=-o*e[0][1];t.scale(150*o).translate([i,u])}),e)}function gr(t,n,e){return hr(t,(function(e){var r=+n,o=r/(e[1][1]-e[0][1]),i=-o*e[0][0],u=(r-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([i,u])}),e)}ur.prototype={_radius:4.5,_circle:ar(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._string.push("M",t,",",n),this._point=1;break;case 1:this._string.push("L",t,",",n);break;default:null==this._circle&&(this._circle=ar(this._radius)),this._string.push("M",t,",",n,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}},sr.prototype={constructor:sr,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var mr=m(30*p);function yr(t,n){return+n?function(t,n){function e(r,o,i,a,c,l,f,s,h,p,v,m,y,w){var _=f-r,b=s-o,E=_*_+b*b;if(E>4*n&&y--){var x=a+p,M=c+v,k=l+m,N=S(x*x+M*M+k*k),P=j(k/=N),A=d(d(k)-1)<u||d(i-h)<u?(i+h)/2:g(M,x),Z=t(A,P),z=Z[0],O=Z[1],C=z-r,T=O-o,L=b*C-_*T;(L*L/E>n||d((_*C+b*T)/E-.5)>.3||a*p+c*v+l*m<mr)&&(e(r,o,i,a,c,l,z,O,A,x/=N,M/=N,k,y,w),w.point(z,O),e(z,O,A,x,M,k,f,s,h,p,v,m,y,w))}}return function(n){var r,o,i,u,a,c,l,f,s,h,p,d,v={point:g,lineStart:m,lineEnd:w,polygonStart:function(){n.polygonStart(),v.lineStart=_},polygonEnd:function(){n.polygonEnd(),v.lineStart=m}};function g(e,r){e=t(e,r),n.point(e[0],e[1])}function m(){f=NaN,v.point=y,n.lineStart()}function y(r,o){var i=ct([r,o]),u=t(r,o);e(f,s,l,h,p,d,f=u[0],s=u[1],l=r,h=i[0],p=i[1],d=i[2],16,n),n.point(f,s)}function w(){v.point=g,n.lineEnd()}function _(){m(),v.point=b,v.lineEnd=E}function b(t,n){y(r=t,n),o=f,i=s,u=h,a=p,c=d,v.point=y}function E(){e(f,s,l,h,p,d,o,i,r,u,a,c,16,n),v.lineEnd=w,w()}return v}}(t,n):function(t){return fr({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}(t)}var wr=fr({point:function(t,n){this.stream.point(t*p,n*p)}});function _r(t,n,e,r,o,i){if(!i)return function(t,n,e,r,o){function i(i,u){return[n+t*(i*=r),e-t*(u*=o)]}return i.invert=function(i,u){return[(i-n)/t*r,(e-u)/t*o]},i}(t,n,e,r,o);var u=m(i),a=x(i),c=u*t,l=a*t,f=u/t,s=a/t,h=(a*e-u*n)/t,p=(a*n+u*e)/t;function d(t,i){return[c*(t*=r)-l*(i*=o)+n,e-l*t-c*i]}return d.invert=function(t,n){return[r*(f*t-s*n+h),o*(p-s*t-f*n)]},d}function br(t){return Er((function(){return t}))()}function Er(t){var n,e,r,o,i,u,a,c,l,f,s=150,d=480,v=250,g=0,m=0,y=0,w=0,_=0,b=0,E=1,x=1,M=null,k=Nn,N=null,j=fe,P=.5;function A(t){return c(t[0]*p,t[1]*p)}function Z(t){return(t=c.invert(t[0],t[1]))&&[t[0]*h,t[1]*h]}function z(){var t=_r(s,0,0,E,x,b).apply(null,n(g,m)),r=_r(s,d-t[0],v-t[1],E,x,b);return e=rn(y,w,_),a=nn(n,r),c=nn(e,a),u=yr(a,P),O()}function O(){return l=f=null,A}return A.stream=function(t){return l&&f===t?l:l=wr(function(t){return fr({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}(e)(k(u(j(f=t)))))},A.preclip=function(t){return arguments.length?(k=t,M=void 0,O()):k},A.postclip=function(t){return arguments.length?(j=t,N=r=o=i=null,O()):j},A.clipAngle=function(t){return arguments.length?(k=+t?jn(M=t*p):(M=null,Nn),O()):M*h},A.clipExtent=function(t){return arguments.length?(j=null==t?(N=r=o=i=null,fe):Tn(N=+t[0][0],r=+t[0][1],o=+t[1][0],i=+t[1][1]),O()):null==N?null:[[N,r],[o,i]]},A.scale=function(t){return arguments.length?(s=+t,z()):s},A.translate=function(t){return arguments.length?(d=+t[0],v=+t[1],z()):[d,v]},A.center=function(t){return arguments.length?(g=t[0]%360*p,m=t[1]%360*p,z()):[g*h,m*h]},A.rotate=function(t){return arguments.length?(y=t[0]%360*p,w=t[1]%360*p,_=t.length>2?t[2]%360*p:0,z()):[y*h,w*h,_*h]},A.angle=function(t){return arguments.length?(b=t%360*p,z()):b*h},A.reflectX=function(t){return arguments.length?(E=t?-1:1,z()):E<0},A.reflectY=function(t){return arguments.length?(x=t?-1:1,z()):x<0},A.precision=function(t){return arguments.length?(u=yr(a,P=t*t),O()):S(P)},A.fitExtent=function(t,n){return pr(A,t,n)},A.fitSize=function(t,n){return dr(A,t,n)},A.fitWidth=function(t,n){return vr(A,t,n)},A.fitHeight=function(t,n){return gr(A,t,n)},function(){return n=t.apply(this,arguments),A.invert=n.invert&&Z,z()}}function xr(t){var n=0,e=c/3,r=Er(t),o=r(n,e);return o.parallels=function(t){return arguments.length?r(n=t[0]*p,e=t[1]*p):[n*h,e*h]},o}function Mr(t,n){var e=x(t),r=(e+x(n))/2;if(d(r)<u)return function(t){var n=m(t);function e(t,e){return[t*n,x(e)/n]}return e.invert=function(t,e){return[t/n,j(e*n)]},e}(t);var o=1+e*(2*r-e),i=S(o)/r;function a(t,n){var e=S(o-2*r*x(n))/r;return[e*x(t*=r),i-e*m(t)]}return a.invert=function(t,n){var e=i-n,u=g(t,d(e))*M(e);return e*r<0&&(u-=c*M(t)*M(e)),[u/r,j((o-(t*t+e*e)*r*r)/(2*r))]},a}function Sr(){return xr(Mr).scale(155.424).center([0,33.6442])}function kr(){return Sr().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function Nr(){var t,n,e,r,o,i,a=kr(),c=Sr().rotate([154,0]).center([-2,58.5]).parallels([55,65]),l=Sr().rotate([157,0]).center([-3,19.9]).parallels([8,18]),f={point:function(t,n){i=[t,n]}};function s(t){var n=t[0],u=t[1];return i=null,e.point(n,u),i||(r.point(n,u),i)||(o.point(n,u),i)}function h(){return t=n=null,s}return s.invert=function(t){var n=a.scale(),e=a.translate(),r=(t[0]-e[0])/n,o=(t[1]-e[1])/n;return(o>=.12&&o<.234&&r>=-.425&&r<-.214?c:o>=.166&&o<.234&&r>=-.214&&r<-.115?l:a).invert(t)},s.stream=function(e){return t&&n===e?t:t=function(t){var n=t.length;return{point:function(e,r){for(var o=-1;++o<n;)t[o].point(e,r)},sphere:function(){for(var e=-1;++e<n;)t[e].sphere()},lineStart:function(){for(var e=-1;++e<n;)t[e].lineStart()},lineEnd:function(){for(var e=-1;++e<n;)t[e].lineEnd()},polygonStart:function(){for(var e=-1;++e<n;)t[e].polygonStart()},polygonEnd:function(){for(var e=-1;++e<n;)t[e].polygonEnd()}}}([a.stream(n=e),c.stream(e),l.stream(e)])},s.precision=function(t){return arguments.length?(a.precision(t),c.precision(t),l.precision(t),h()):a.precision()},s.scale=function(t){return arguments.length?(a.scale(t),c.scale(.35*t),l.scale(t),s.translate(a.translate())):a.scale()},s.translate=function(t){if(!arguments.length)return a.translate();var n=a.scale(),i=+t[0],s=+t[1];return e=a.translate(t).clipExtent([[i-.455*n,s-.238*n],[i+.455*n,s+.238*n]]).stream(f),r=c.translate([i-.307*n,s+.201*n]).clipExtent([[i-.425*n+u,s+.12*n+u],[i-.214*n-u,s+.234*n-u]]).stream(f),o=l.translate([i-.205*n,s+.212*n]).clipExtent([[i-.214*n+u,s+.166*n+u],[i-.115*n-u,s+.234*n-u]]).stream(f),h()},s.fitExtent=function(t,n){return pr(s,t,n)},s.fitSize=function(t,n){return dr(s,t,n)},s.fitWidth=function(t,n){return vr(s,t,n)},s.fitHeight=function(t,n){return gr(s,t,n)},s.scale(1070)}function jr(t){return function(n,e){var r=m(n),o=m(e),i=t(r*o);return i===1/0?[2,0]:[i*o*x(n),i*x(e)]}}function Pr(t){return function(n,e){var r=S(n*n+e*e),o=t(r),i=x(o),u=m(o);return[g(n*i,r*u),j(r&&e*i/r)]}}var Ar=jr((function(t){return S(2/(1+t))}));function Zr(){return br(Ar).scale(124.75).clipAngle(179.999)}Ar.invert=Pr((function(t){return 2*j(t/2)}));var zr=jr((function(t){return(t=N(t))&&t/x(t)}));function Or(){return br(zr).scale(79.4188).clipAngle(179.999)}function Cr(t,n){return[t,b(k((l+n)/2))]}function Tr(){return Lr(Cr).scale(961/s)}function Lr(t){var n,e,r,o=br(t),i=o.center,u=o.scale,a=o.translate,l=o.clipExtent,f=null;function s(){var i=c*u(),a=o(cn(o.rotate()).invert([0,0]));return l(null==f?[[a[0]-i,a[1]-i],[a[0]+i,a[1]+i]]:t===Cr?[[Math.max(a[0]-i,f),n],[Math.min(a[0]+i,e),r]]:[[f,Math.max(a[1]-i,n)],[e,Math.min(a[1]+i,r)]])}return o.scale=function(t){return arguments.length?(u(t),s()):u()},o.translate=function(t){return arguments.length?(a(t),s()):a()},o.center=function(t){return arguments.length?(i(t),s()):i()},o.clipExtent=function(t){return arguments.length?(null==t?f=n=e=r=null:(f=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),s()):null==f?null:[[f,n],[e,r]]},s()}function Rr(t){return k((l+t)/2)}function qr(t,n){var e=m(t),r=t===n?x(t):b(e/m(n))/b(Rr(n)/Rr(t)),o=e*E(Rr(t),r)/r;if(!r)return Cr;function i(t,n){o>0?n<-l+u&&(n=-l+u):n>l-u&&(n=l-u);var e=o/E(Rr(n),r);return[e*x(r*t),o-e*m(r*t)]}return i.invert=function(t,n){var e=o-n,i=M(r)*S(t*t+e*e),u=g(t,d(e))*M(e);return e*r<0&&(u-=c*M(t)*M(e)),[u/r,2*v(E(o/i,1/r))-l]},i}function Gr(){return xr(qr).scale(109.5).parallels([30,30])}function Yr(t,n){return[t,n]}function Dr(){return br(Yr).scale(152.63)}function Ir(t,n){var e=m(t),r=t===n?x(t):(e-m(n))/(n-t),o=e/r+t;if(d(r)<u)return Yr;function i(t,n){var e=o-n,i=r*t;return[e*x(i),o-e*m(i)]}return i.invert=function(t,n){var e=o-n,i=g(t,d(e))*M(e);return e*r<0&&(i-=c*M(t)*M(e)),[i/r,o-M(r)*S(t*t+e*e)]},i}function Xr(){return xr(Ir).scale(131.154).center([0,13.9389])}zr.invert=Pr((function(t){return t})),Cr.invert=function(t,n){return[t,2*v(w(n))-l]},Yr.invert=Yr;var Fr=1.340264,Br=-.081106,Ur=893e-6,Hr=.003796,Vr=S(3)/2;function Wr(t,n){var e=j(Vr*x(n)),r=e*e,o=r*r*r;return[t*m(e)/(Vr*(Fr+3*Br*r+o*(7*Ur+9*Hr*r))),e*(Fr+Br*r+o*(Ur+Hr*r))]}function Kr(){return br(Wr).scale(177.158)}function $r(t,n){var e=m(n),r=m(t)*e;return[e*x(t)/r,x(n)/r]}function Jr(){return br($r).scale(144.049).clipAngle(60)}function Qr(){var t,n,e,r,o,i,u,a=1,c=0,l=0,f=1,s=1,d=0,v=null,g=1,y=1,w=fr({point:function(t,n){var e=E([t,n]);this.stream.point(e[0],e[1])}}),_=fe;function b(){return g=a*f,y=a*s,i=u=null,E}function E(e){var r=e[0]*g,o=e[1]*y;if(d){var i=o*t-r*n;r=r*t+o*n,o=i}return[r+c,o+l]}return E.invert=function(e){var r=e[0]-c,o=e[1]-l;if(d){var i=o*t+r*n;r=r*t-o*n,o=i}return[r/g,o/y]},E.stream=function(t){return i&&u===t?i:i=w(_(u=t))},E.postclip=function(t){return arguments.length?(_=t,v=e=r=o=null,b()):_},E.clipExtent=function(t){return arguments.length?(_=null==t?(v=e=r=o=null,fe):Tn(v=+t[0][0],e=+t[0][1],r=+t[1][0],o=+t[1][1]),b()):null==v?null:[[v,e],[r,o]]},E.scale=function(t){return arguments.length?(a=+t,b()):a},E.translate=function(t){return arguments.length?(c=+t[0],l=+t[1],b()):[c,l]},E.angle=function(e){return arguments.length?(n=x(d=e%360*p),t=m(d),b()):d*h},E.reflectX=function(t){return arguments.length?(f=t?-1:1,b()):f<0},E.reflectY=function(t){return arguments.length?(s=t?-1:1,b()):s<0},E.fitExtent=function(t,n){return pr(E,t,n)},E.fitSize=function(t,n){return dr(E,t,n)},E.fitWidth=function(t,n){return vr(E,t,n)},E.fitHeight=function(t,n){return gr(E,t,n)},E}function to(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(r*(.003971*e-.001529*r)-.013791)),n*(1.007226+e*(.015085+r*(.028874*e-.044475-.005916*r)))]}function no(){return br(to).scale(175.295)}function eo(t,n){return[m(n)*x(t),x(n)]}function ro(){return br(eo).scale(249.5).clipAngle(90.000001)}function oo(t,n){var e=m(n),r=1+m(t)*e;return[e*x(t)/r,x(n)/r]}function io(){return br(oo).scale(250).clipAngle(142)}function uo(t,n){return[b(k((l+n)/2)),-t]}function ao(){var t=Lr(uo),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)}Wr.invert=function(t,n){for(var e,r=n,o=r*r,i=o*o*o,u=0;u<12&&(i=(o=(r-=e=(r*(Fr+Br*o+i*(Ur+Hr*o))-n)/(Fr+3*Br*o+i*(7*Ur+9*Hr*o)))*r)*o*o,!(d(e)<a));++u);return[Vr*t*(Fr+3*Br*o+i*(7*Ur+9*Hr*o))/m(r),j(x(r)/Vr)]},$r.invert=Pr(v),to.invert=function(t,n){var e,r=n,o=25;do{var i=r*r,a=i*i;r-=e=(r*(1.007226+i*(.015085+a*(.028874*i-.044475-.005916*a)))-n)/(1.007226+i*(.045255+a*(.259866*i-.311325-.005916*11*a)))}while(d(e)>u&&--o>0);return[t/(.8707+(i=r*r)*(i*(i*i*i*(.003971-.001529*i)-.013791)-.131979)),r]},eo.invert=Pr(j),oo.invert=Pr((function(t){return 2*v(t)})),uo.invert=function(t,n){return[-n,2*v(w(t))-l]}},89491:function(t,n,e){"use strict";function r(t){return"object"===typeof t&&"length"in t?t:Array.from(t)}e.d(n,{Z:function(){return r}})},8413:function(t,n,e){"use strict";e.d(n,{Z:function(){return a}});var r=e(46229),o=e(86094);function i(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===o.P&&n.documentElement.namespaceURI===o.P?n.createElement(t):n.createElementNS(e,t)}}function u(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function a(t){var n=(0,r.Z)(t);return(n.local?u:i)(n)}},32320:function(t,n,e){"use strict";e.r(n),e.d(n,{create:function(){return i},creator:function(){return r.Z},local:function(){return a},matcher:function(){return l.Z},namespace:function(){return f.Z},namespaces:function(){return s.Z},pointer:function(){return h.Z},pointers:function(){return d},select:function(){return o.Z},selectAll:function(){return m},selection:function(){return g.ZP},selector:function(){return y.Z},selectorAll:function(){return w.Z},style:function(){return _.S},window:function(){return b.Z}});var r=e(8413),o=e(49261);function i(t){return(0,o.Z)((0,r.Z)(t).call(document.documentElement))}var u=0;function a(){return new c}function c(){this._="@"+(++u).toString(36)}c.prototype=a.prototype={constructor:c,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};var l=e(51761),f=e(46229),s=e(86094),h=e(29439),p=e(20252);function d(t,n){return t.target&&(t=(0,p.Z)(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,(function(t){return(0,h.Z)(t,n)}))}var v=e(89491),g=e(29426);function m(t){return"string"===typeof t?new g.Y1([document.querySelectorAll(t)],[document.documentElement]):new g.Y1([null==t?[]:(0,v.Z)(t)],g.Jz)}var y=e(12538),w=e(84265),_=e(20946),b=e(75795)},51761:function(t,n,e){"use strict";function r(t){return function(){return this.matches(t)}}function o(t){return function(n){return n.matches(t)}}e.d(n,{P:function(){return o},Z:function(){return r}})},46229:function(t,n,e){"use strict";e.d(n,{Z:function(){return o}});var r=e(86094);function o(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),r.Z.hasOwnProperty(n)?{space:r.Z[n],local:t}:t}},86094:function(t,n,e){"use strict";e.d(n,{P:function(){return r}});var r="http://www.w3.org/1999/xhtml";n.Z={svg:"http://www.w3.org/2000/svg",xhtml:r,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"}},29439:function(t,n,e){"use strict";e.d(n,{Z:function(){return o}});var r=e(20252);function o(t,n){if(t=(0,r.Z)(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var o=e.createSVGPoint();return o.x=t.clientX,o.y=t.clientY,[(o=o.matrixTransform(n.getScreenCTM().inverse())).x,o.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}},49261:function(t,n,e){"use strict";e.d(n,{Z:function(){return o}});var r=e(29426);function o(t){return"string"===typeof t?new r.Y1([[document.querySelector(t)]],[document.documentElement]):new r.Y1([[t]],r.Jz)}},29426:function(t,n,e){"use strict";e.d(n,{Y1:function(){return lt},ZP:function(){return st},Jz:function(){return ct}});var r=e(4942),o=e(12538);var i=e(89491),u=e(84265);var a=e(51761),c=Array.prototype.find;function l(){return this.firstElementChild}var f=Array.prototype.filter;function s(){return this.children}function h(t){return new Array(t.length)}function p(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function d(t){return function(){return t}}function v(t,n,e,r,o,i){for(var u,a=0,c=n.length,l=i.length;a<l;++a)(u=n[a])?(u.__data__=i[a],r[a]=u):e[a]=new p(t,i[a]);for(;a<c;++a)(u=n[a])&&(o[a]=u)}function g(t,n,e,r,o,i,u){var a,c,l,f=new Map,s=n.length,h=i.length,d=new Array(s);for(a=0;a<s;++a)(c=n[a])&&(d[a]=l=u.call(c,c.__data__,a,n)+"",f.has(l)?o[a]=c:f.set(l,c));for(a=0;a<h;++a)l=u.call(t,i[a],a,i)+"",(c=f.get(l))?(r[a]=c,c.__data__=i[a],f.delete(l)):e[a]=new p(t,i[a]);for(a=0;a<s;++a)(c=n[a])&&f.get(d[a])===c&&(o[a]=c)}function m(t){return t.__data__}function y(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}p.prototype={constructor:p,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var w=e(37762);var _=e(46229);function b(t){return function(){this.removeAttribute(t)}}function E(t){return function(){this.removeAttributeNS(t.space,t.local)}}function x(t,n){return function(){this.setAttribute(t,n)}}function M(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}function S(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}function k(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}var N=e(20946);function j(t){return function(){delete this[t]}}function P(t,n){return function(){this[t]=n}}function A(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}function Z(t){return t.trim().split(/^|\s+/)}function z(t){return t.classList||new O(t)}function O(t){this._node=t,this._names=Z(t.getAttribute("class")||"")}function C(t,n){for(var e=z(t),r=-1,o=n.length;++r<o;)e.add(n[r])}function T(t,n){for(var e=z(t),r=-1,o=n.length;++r<o;)e.remove(n[r])}function L(t){return function(){C(this,t)}}function R(t){return function(){T(this,t)}}function q(t,n){return function(){(n.apply(this,arguments)?C:T)(this,t)}}function G(){this.textContent=""}function Y(t){return function(){this.textContent=t}}function D(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}function I(){this.innerHTML=""}function X(t){return function(){this.innerHTML=t}}function F(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}function B(){this.nextSibling&&this.parentNode.appendChild(this)}function U(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}O.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var H=e(8413);function V(){return null}function W(){var t=this.parentNode;t&&t.removeChild(this)}function K(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function $(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function J(t){return t.trim().split(/^|\s+/).map((function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}))}function Q(t){return function(){var n=this.__on;if(n){for(var e,r=0,o=-1,i=n.length;r<i;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++o]=e:this.removeEventListener(e.type,e.listener,e.options);++o?n.length=o:delete this.__on}}}function tt(t,n,e){return function(){var r,o=this.__on,i=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(o)for(var u=0,a=o.length;u<a;++u)if((r=o[u]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=i,r.options=e),void(r.value=n);this.addEventListener(t.type,i,e),r={type:t.type,name:t.name,value:n,listener:i,options:e},o?o.push(r):this.__on=[r]}}var nt=e(75795);function et(t,n,e){var r=(0,nt.Z)(t),o=r.CustomEvent;"function"===typeof o?o=new o(n,e):(o=r.document.createEvent("Event"),e?(o.initEvent(n,e.bubbles,e.cancelable),o.detail=e.detail):o.initEvent(n,!1,!1)),t.dispatchEvent(o)}function rt(t,n){return function(){return et(this,t,n)}}function ot(t,n){return function(){return et(this,t,n.apply(this,arguments))}}var it=e(74165),ut=(0,it.Z)().mark(at);function at(){var t,n,e,r,o,i,u;return(0,it.Z)().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:t=this._groups,n=0,e=t.length;case 1:if(!(n<e)){a.next=13;break}r=t[n],o=0,i=r.length;case 3:if(!(o<i)){a.next=10;break}if(!(u=r[o])){a.next=7;break}return a.next=7,u;case 7:++o,a.next=3;break;case 10:++n,a.next=1;break;case 13:case"end":return a.stop()}}),ut,this)}var ct=[null];function lt(t,n){this._groups=t,this._parents=n}function ft(){return new lt([[document.documentElement]],ct)}lt.prototype=ft.prototype=(0,r.Z)({constructor:lt,select:function(t){"function"!==typeof t&&(t=(0,o.Z)(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var u,a,c=n[i],l=c.length,f=r[i]=new Array(l),s=0;s<l;++s)(u=c[s])&&(a=t.call(u,u.__data__,s,c))&&("__data__"in u&&(a.__data__=u.__data__),f[s]=a);return new lt(r,this._parents)},selectAll:function(t){t="function"===typeof t?function(t){return function(){var n=t.apply(this,arguments);return null==n?[]:(0,i.Z)(n)}}(t):(0,u.Z)(t);for(var n=this._groups,e=n.length,r=[],o=[],a=0;a<e;++a)for(var c,l=n[a],f=l.length,s=0;s<f;++s)(c=l[s])&&(r.push(t.call(c,c.__data__,s,l)),o.push(c));return new lt(r,o)},selectChild:function(t){return this.select(null==t?l:function(t){return function(){return c.call(this.children,t)}}("function"===typeof t?t:(0,a.P)(t)))},selectChildren:function(t){return this.selectAll(null==t?s:function(t){return function(){return f.call(this.children,t)}}("function"===typeof t?t:(0,a.P)(t)))},filter:function(t){"function"!==typeof t&&(t=(0,a.Z)(t));for(var n=this._groups,e=n.length,r=new Array(e),o=0;o<e;++o)for(var i,u=n[o],c=u.length,l=r[o]=[],f=0;f<c;++f)(i=u[f])&&t.call(i,i.__data__,f,u)&&l.push(i);return new lt(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,m);var e=n?g:v,r=this._parents,o=this._groups;"function"!==typeof t&&(t=d(t));for(var u=o.length,a=new Array(u),c=new Array(u),l=new Array(u),f=0;f<u;++f){var s=r[f],h=o[f],p=h.length,y=(0,i.Z)(t.call(s,s&&s.__data__,f,r)),w=y.length,_=c[f]=new Array(w),b=a[f]=new Array(w),E=l[f]=new Array(p);e(s,h,_,b,E,y,n);for(var x,M,S=0,k=0;S<w;++S)if(x=_[S]){for(S>=k&&(k=S+1);!(M=b[k])&&++k<w;);x._next=M||null}}return(a=new lt(a,r))._enter=c,a._exit=l,a},enter:function(){return new lt(this._enter||this._groups.map(h),this._parents)},exit:function(){return new lt(this._exit||this._groups.map(h),this._parents)},join:function(t,n,e){var r=this.enter(),o=this,i=this.exit();return r="function"===typeof t?t(r):r.append(t+""),null!=n&&(o=n(o)),null==e?i.remove():e(i),r&&o?r.merge(o).order():o},merge:function(t){if(!(t instanceof lt))throw new Error("invalid merge");for(var n=this._groups,e=t._groups,r=n.length,o=e.length,i=Math.min(r,o),u=new Array(r),a=0;a<i;++a)for(var c,l=n[a],f=e[a],s=l.length,h=u[a]=new Array(s),p=0;p<s;++p)(c=l[p]||f[p])&&(h[p]=c);for(;a<r;++a)u[a]=n[a];return new lt(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,o=t[n],i=o.length-1,u=o[i];--i>=0;)(r=o[i])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=y);for(var e=this._groups,r=e.length,o=new Array(r),i=0;i<r;++i){for(var u,a=e[i],c=a.length,l=o[i]=new Array(c),f=0;f<c;++f)(u=a[f])&&(l[f]=u);l.sort(n)}return new lt(o,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],o=0,i=r.length;o<i;++o){var u=r[o];if(u)return u}return null},size:function(){var t,n=0,e=(0,w.Z)(this);try{for(e.s();!(t=e.n()).done;){t.value;++n}}catch(r){e.e(r)}finally{e.f()}return n},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var o,i=n[e],u=0,a=i.length;u<a;++u)(o=i[u])&&t.call(o,o.__data__,u,i);return this},attr:function(t,n){var e=(0,_.Z)(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?E:b:"function"===typeof n?e.local?k:S:e.local?M:x)(e,n))},style:N.Z,property:function(t,n){return arguments.length>1?this.each((null==n?j:"function"===typeof n?A:P)(t,n)):this.node()[t]},classed:function(t,n){var e=Z(t+"");if(arguments.length<2){for(var r=z(this.node()),o=-1,i=e.length;++o<i;)if(!r.contains(e[o]))return!1;return!0}return this.each(("function"===typeof n?q:n?L:R)(e,n))},text:function(t){return arguments.length?this.each(null==t?G:("function"===typeof t?D:Y)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?I:("function"===typeof t?F:X)(t)):this.node().innerHTML},raise:function(){return this.each(B)},lower:function(){return this.each(U)},append:function(t){var n="function"===typeof t?t:(0,H.Z)(t);return this.select((function(){return this.appendChild(n.apply(this,arguments))}))},insert:function(t,n){var e="function"===typeof t?t:(0,H.Z)(t),r=null==n?V:"function"===typeof n?n:(0,o.Z)(n);return this.select((function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each(W)},clone:function(t){return this.select(t?$:K)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,o,i=J(t+""),u=i.length;if(!(arguments.length<2)){for(a=n?tt:Q,r=0;r<u;++r)this.each(a(i[r],n,e));return this}var a=this.node().__on;if(a)for(var c,l=0,f=a.length;l<f;++l)for(r=0,c=a[l];r<u;++r)if((o=i[r]).type===c.type&&o.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"===typeof n?ot:rt)(t,n))}},Symbol.iterator,at);var st=ft},20946:function(t,n,e){"use strict";e.d(n,{S:function(){return c},Z:function(){return a}});var r=e(75795);function o(t){return function(){this.style.removeProperty(t)}}function i(t,n,e){return function(){this.style.setProperty(t,n,e)}}function u(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}function a(t,n,e){return arguments.length>1?this.each((null==n?o:"function"===typeof n?u:i)(t,n,null==e?"":e)):c(this.node(),t)}function c(t,n){return t.style.getPropertyValue(n)||(0,r.Z)(t).getComputedStyle(t,null).getPropertyValue(n)}},12538:function(t,n,e){"use strict";function r(){}function o(t){return null==t?r:function(){return this.querySelector(t)}}e.d(n,{Z:function(){return o}})},84265:function(t,n,e){"use strict";function r(){return[]}function o(t){return null==t?r:function(){return this.querySelectorAll(t)}}e.d(n,{Z:function(){return o}})},20252:function(t,n,e){"use strict";function r(t){for(var n;n=t.sourceEvent;)t=n;return t}e.d(n,{Z:function(){return r}})},75795:function(t,n,e){"use strict";function r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}e.d(n,{Z:function(){return r}})},39839:function(t,n,e){"use strict";e.r(n),e.d(n,{zoom:function(){return Ln},zoomIdentity:function(){return kn},zoomTransform:function(){return Nn}});var r={value:function(){}};function o(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new i(r)}function i(t){this._=t}function u(t,n){return t.trim().split(/^|\s+/).map((function(t){var e="",r=t.indexOf(".");if(r>=0&&(e=t.slice(r+1),t=t.slice(0,r)),t&&!n.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))}function a(t,n){for(var e,r=0,o=t.length;r<o;++r)if((e=t[r]).name===n)return e.value}function c(t,n,e){for(var o=0,i=t.length;o<i;++o)if(t[o].name===n){t[o]=r,t=t.slice(0,o).concat(t.slice(o+1));break}return null!=e&&t.push({name:n,value:e}),t}i.prototype=o.prototype={constructor:i,on:function(t,n){var e,r=this._,o=u(t+"",r),i=-1,l=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!==typeof n)throw new Error("invalid callback: "+n);for(;++i<l;)if(e=(t=o[i]).type)r[e]=c(r[e],t.name,n);else if(null==n)for(e in r)r[e]=c(r[e],t.name,null);return this}for(;++i<l;)if((e=(t=o[i]).type)&&(e=a(r[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new i(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,o=new Array(e),i=0;i<e;++i)o[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(i=0,e=(r=this._[t]).length;i<e;++i)r[i].value.apply(n,o)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],o=0,i=r.length;o<i;++o)r[o].value.apply(n,e)}};var l=o,f=e(49261);function s(t){t.preventDefault(),t.stopImmediatePropagation()}function h(t){var n=t.document.documentElement,e=(0,f.Z)(t).on("dragstart.drag",s,!0);"onselectstart"in n?e.on("selectstart.drag",s,!0):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function p(t,n){var e=t.document.documentElement,r=(0,f.Z)(t).on("dragstart.drag",null);n&&(r.on("click.drag",s,!0),setTimeout((function(){r.on("click.drag",null)}),0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}function d(t){return((t=Math.exp(t))+1/t)/2}var v,g,m=function t(n,e,r){function o(t,o){var i,u,a=t[0],c=t[1],l=t[2],f=o[0],s=o[1],h=o[2],p=f-a,v=s-c,g=p*p+v*v;if(g<1e-12)u=Math.log(h/l)/n,i=function(t){return[a+t*p,c+t*v,l*Math.exp(n*t*u)]};else{var m=Math.sqrt(g),y=(h*h-l*l+r*g)/(2*l*e*m),w=(h*h-l*l-r*g)/(2*h*e*m),_=Math.log(Math.sqrt(y*y+1)-y),b=Math.log(Math.sqrt(w*w+1)-w);u=(b-_)/n,i=function(t){var r,o=t*u,i=d(_),f=l/(e*m)*(i*(r=n*o+_,((r=Math.exp(2*r))-1)/(r+1))-function(t){return((t=Math.exp(t))-1/t)/2}(_));return[a+f*p,c+f*v,l*i/d(n*o+_)]}}return i.duration=1e3*u*n/Math.SQRT2,i}return o.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},o}(Math.SQRT2,2,4),y=e(29439),w=e(29426),_=0,b=0,E=0,x=0,M=0,S=0,k="object"===typeof performance&&performance.now?performance:Date,N="object"===typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function j(){return M||(N(P),M=k.now()+S)}function P(){M=0}function A(){this._call=this._time=this._next=null}function Z(t,n,e){var r=new A;return r.restart(t,n,e),r}function z(){M=(x=k.now())+S,_=b=0;try{!function(){j(),++_;for(var t,n=v;n;)(t=M-n._time)>=0&&n._call.call(null,t),n=n._next;--_}()}finally{_=0,function(){var t,n,e=v,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:v=n);g=t,C(r)}(),M=0}}function O(){var t=k.now(),n=t-x;n>1e3&&(S-=n,x=t)}function C(t){_||(b&&(b=clearTimeout(b)),t-M>24?(t<1/0&&(b=setTimeout(z,t-k.now()-S)),E&&(E=clearInterval(E))):(E||(x=k.now(),E=setInterval(O,1e3)),_=1,N(z)))}function T(t,n,e){var r=new A;return n=null==n?0:+n,r.restart((function(e){r.stop(),t(e+n)}),n,e),r}A.prototype=Z.prototype={constructor:A,restart:function(t,n,e){if("function"!==typeof t)throw new TypeError("callback is not a function");e=(null==e?j():+e)+(null==n?0:+n),this._next||g===this||(g?g._next=this:v=this,g=this),this._call=t,this._time=e,C()},stop:function(){this._call&&(this._call=null,this._time=1/0,C())}};var L=l("start","end","cancel","interrupt"),R=[];function q(t,n,e,r,o,i){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,o=t.__transition;function i(t){e.state=1,e.timer.restart(u,e.delay,e.time),e.delay<=t&&u(t-e.delay)}function u(i){var l,f,s,h;if(1!==e.state)return c();for(l in o)if((h=o[l]).name===e.name){if(3===h.state)return T(u);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete o[l]):+l<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete o[l])}if(T((function(){3===e.state&&(e.state=4,e.timer.restart(a,e.delay,e.time),a(i))})),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(e.state=3,r=new Array(s=e.tween.length),l=0,f=-1;l<s;++l)(h=e.tween[l].value.call(t,t.__data__,e.index,e.group))&&(r[++f]=h);r.length=f+1}}function a(n){for(var o=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(c),e.state=5,1),i=-1,u=r.length;++i<u;)r[i].call(t,o);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),c())}function c(){for(var r in e.state=6,e.timer.stop(),delete o[n],o)return;delete t.__transition}o[n]=e,e.timer=Z(i,0,e.time)}(t,e,{name:n,index:r,group:o,on:L,tween:R,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function G(t,n){var e=D(t,n);if(e.state>0)throw new Error("too late; already scheduled");return e}function Y(t,n){var e=D(t,n);if(e.state>3)throw new Error("too late; already running");return e}function D(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function I(t,n){var e,r,o,i=t.__transition,u=!0;if(i){for(o in n=null==n?null:n+"",i)(e=i[o]).name===n?(r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete i[o]):u=!1;u&&delete t.__transition}}var X=e(4942);function F(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}var B,U=180/Math.PI,H={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function V(t,n,e,r,o,i){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:o,translateY:i,rotate:Math.atan2(n,t)*U,skewX:Math.atan(c)*U,scaleX:u,scaleY:a}}function W(t,n,e,r){function o(t){return t.length?t.pop()+" ":""}return function(i,u){var a=[],c=[];return i=t(i),u=t(u),function(t,r,o,i,u,a){if(t!==o||r!==i){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:F(t,o)},{i:c-2,x:F(r,i)})}else(o||i)&&u.push("translate("+o+n+i+e)}(i.translateX,i.translateY,u.translateX,u.translateY,a,c),function(t,n,e,i){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),i.push({i:e.push(o(e)+"rotate(",null,r)-2,x:F(t,n)})):n&&e.push(o(e)+"rotate("+n+r)}(i.rotate,u.rotate,a,c),function(t,n,e,i){t!==n?i.push({i:e.push(o(e)+"skewX(",null,r)-2,x:F(t,n)}):n&&e.push(o(e)+"skewX("+n+r)}(i.skewX,u.skewX,a,c),function(t,n,e,r,i,u){if(t!==e||n!==r){var a=i.push(o(i)+"scale(",null,",",null,")");u.push({i:a-4,x:F(t,e)},{i:a-2,x:F(n,r)})}else 1===e&&1===r||i.push(o(i)+"scale("+e+","+r+")")}(i.scaleX,i.scaleY,u.scaleX,u.scaleY,a,c),i=u=null,function(t){for(var n,e=-1,r=c.length;++e<r;)a[(n=c[e]).i]=n.x(t);return a.join("")}}}var K=W((function(t){var n=new("function"===typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?H:V(n.a,n.b,n.c,n.d,n.e,n.f)}),"px, ","px)","deg)"),$=W((function(t){return null==t?H:(B||(B=document.createElementNS("http://www.w3.org/2000/svg","g")),B.setAttribute("transform",t),(t=B.transform.baseVal.consolidate())?V((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):H)}),", ",")",")"),J=e(46229);function Q(t,n){var e,r;return function(){var o=Y(this,t),i=o.tween;if(i!==e)for(var u=0,a=(r=e=i).length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}o.tween=r}}function tt(t,n,e){var r,o;if("function"!==typeof e)throw new Error;return function(){var i=Y(this,t),u=i.tween;if(u!==r){o=(r=u).slice();for(var a={name:n,value:e},c=0,l=o.length;c<l;++c)if(o[c].name===n){o[c]=a;break}c===l&&o.push(a)}i.tween=o}}function nt(t,n,e){var r=t._id;return t.each((function(){var t=Y(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)})),function(t){return D(t,r).value[n]}}function et(t,n,e){t.prototype=n.prototype=e,e.constructor=t}function rt(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function ot(){}var it=.7,ut=1/it,at="\\s*([+-]?\\d+)\\s*",ct="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",lt="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ft=/^#([0-9a-f]{3,8})$/,st=new RegExp("^rgb\\("+[at,at,at]+"\\)$"),ht=new RegExp("^rgb\\("+[lt,lt,lt]+"\\)$"),pt=new RegExp("^rgba\\("+[at,at,at,ct]+"\\)$"),dt=new RegExp("^rgba\\("+[lt,lt,lt,ct]+"\\)$"),vt=new RegExp("^hsl\\("+[ct,lt,lt]+"\\)$"),gt=new RegExp("^hsla\\("+[ct,lt,lt,ct]+"\\)$"),mt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function yt(){return this.rgb().formatHex()}function wt(){return this.rgb().formatRgb()}function _t(t){var n,e;return t=(t+"").trim().toLowerCase(),(n=ft.exec(t))?(e=n[1].length,n=parseInt(n[1],16),6===e?bt(n):3===e?new St(n>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):8===e?Et(n>>24&255,n>>16&255,n>>8&255,(255&n)/255):4===e?Et(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|240&n,((15&n)<<4|15&n)/255):null):(n=st.exec(t))?new St(n[1],n[2],n[3],1):(n=ht.exec(t))?new St(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=pt.exec(t))?Et(n[1],n[2],n[3],n[4]):(n=dt.exec(t))?Et(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=vt.exec(t))?Pt(n[1],n[2]/100,n[3]/100,1):(n=gt.exec(t))?Pt(n[1],n[2]/100,n[3]/100,n[4]):mt.hasOwnProperty(t)?bt(mt[t]):"transparent"===t?new St(NaN,NaN,NaN,0):null}function bt(t){return new St(t>>16&255,t>>8&255,255&t,1)}function Et(t,n,e,r){return r<=0&&(t=n=e=NaN),new St(t,n,e,r)}function xt(t){return t instanceof ot||(t=_t(t)),t?new St((t=t.rgb()).r,t.g,t.b,t.opacity):new St}function Mt(t,n,e,r){return 1===arguments.length?xt(t):new St(t,n,e,null==r?1:r)}function St(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function kt(){return"#"+jt(this.r)+jt(this.g)+jt(this.b)}function Nt(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}function jt(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function Pt(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Zt(t,n,e,r)}function At(t){if(t instanceof Zt)return new Zt(t.h,t.s,t.l,t.opacity);if(t instanceof ot||(t=_t(t)),!t)return new Zt;if(t instanceof Zt)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,o=Math.min(n,e,r),i=Math.max(n,e,r),u=NaN,a=i-o,c=(i+o)/2;return a?(u=n===i?(e-r)/a+6*(e<r):e===i?(r-n)/a+2:(n-e)/a+4,a/=c<.5?i+o:2-i-o,u*=60):a=c>0&&c<1?0:u,new Zt(u,a,c,t.opacity)}function Zt(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function zt(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}function Ot(t,n,e,r,o){var i=t*t,u=i*t;return((1-3*t+3*i-u)*n+(4-6*i+3*u)*e+(1+3*t+3*i-3*u)*r+u*o)/6}et(ot,_t,{copy:function(t){return Object.assign(new this.constructor,this,t)},displayable:function(){return this.rgb().displayable()},hex:yt,formatHex:yt,formatHsl:function(){return At(this).formatHsl()},formatRgb:wt,toString:wt}),et(St,Mt,rt(ot,{brighter:function(t){return t=null==t?ut:Math.pow(ut,t),new St(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?it:Math.pow(it,t),new St(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:kt,formatHex:kt,formatRgb:Nt,toString:Nt})),et(Zt,(function(t,n,e,r){return 1===arguments.length?At(t):new Zt(t,n,e,null==r?1:r)}),rt(ot,{brighter:function(t){return t=null==t?ut:Math.pow(ut,t),new Zt(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?it:Math.pow(it,t),new Zt(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,o=2*e-r;return new St(zt(t>=240?t-240:t+120,o,r),zt(t,o,r),zt(t<120?t+240:t-120,o,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"hsl(":"hsla(")+(this.h||0)+", "+100*(this.s||0)+"%, "+100*(this.l||0)+"%"+(1===t?")":", "+t+")")}}));var Ct=function(t){return function(){return t}};function Tt(t,n){return function(e){return t+e*n}}function Lt(t){return 1===(t=+t)?Rt:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):Ct(isNaN(n)?e:n)}}function Rt(t,n){var e=n-t;return e?Tt(t,e):Ct(isNaN(t)?n:t)}var qt=function t(n){var e=Lt(n);function r(t,n){var r=e((t=Mt(t)).r,(n=Mt(n)).r),o=e(t.g,n.g),i=e(t.b,n.b),u=Rt(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=o(n),t.b=i(n),t.opacity=u(n),t+""}}return r.gamma=t,r}(1);function Gt(t){return function(n){var e,r,o=n.length,i=new Array(o),u=new Array(o),a=new Array(o);for(e=0;e<o;++e)r=Mt(n[e]),i[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return i=t(i),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=i(t),r.g=u(t),r.b=a(t),r+""}}}Gt((function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),o=t[r],i=t[r+1],u=r>0?t[r-1]:2*o-i,a=r<n-1?t[r+2]:2*i-o;return Ot((e-r/n)*n,u,o,i,a)}})),Gt((function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),o=t[(r+n-1)%n],i=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return Ot((e-r/n)*n,o,i,u,a)}}));var Yt=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Dt=new RegExp(Yt.source,"g");function It(t,n){var e,r,o,i=Yt.lastIndex=Dt.lastIndex=0,u=-1,a=[],c=[];for(t+="",n+="";(e=Yt.exec(t))&&(r=Dt.exec(n));)(o=r.index)>i&&(o=n.slice(i,o),a[u]?a[u]+=o:a[++u]=o),(e=e[0])===(r=r[0])?a[u]?a[u]+=r:a[++u]=r:(a[++u]=null,c.push({i:u,x:F(e,r)})),i=Dt.lastIndex;return i<n.length&&(o=n.slice(i),a[u]?a[u]+=o:a[++u]=o),a.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)a[(e=c[r]).i]=e.x(t);return a.join("")})}function Xt(t,n){var e;return("number"===typeof n?F:n instanceof _t?qt:(e=_t(n))?(n=e,qt):It)(t,n)}function Ft(t){return function(){this.removeAttribute(t)}}function Bt(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Ut(t,n,e){var r,o,i=e+"";return function(){var u=this.getAttribute(t);return u===i?null:u===r?o:o=n(r=u,e)}}function Ht(t,n,e){var r,o,i=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===i?null:u===r?o:o=n(r=u,e)}}function Vt(t,n,e){var r,o,i;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttribute(t))===(a=c+"")?null:u===r&&a===o?i:(o=a,i=n(r=u,c));this.removeAttribute(t)}}function Wt(t,n,e){var r,o,i;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttributeNS(t.space,t.local))===(a=c+"")?null:u===r&&a===o?i:(o=a,i=n(r=u,c));this.removeAttributeNS(t.space,t.local)}}function Kt(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}function $t(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}function Jt(t,n){var e,r;function o(){var o=n.apply(this,arguments);return o!==r&&(e=(r=o)&&$t(t,o)),e}return o._value=n,o}function Qt(t,n){var e,r;function o(){var o=n.apply(this,arguments);return o!==r&&(e=(r=o)&&Kt(t,o)),e}return o._value=n,o}function tn(t,n){return function(){G(this,t).delay=+n.apply(this,arguments)}}function nn(t,n){return n=+n,function(){G(this,t).delay=n}}function en(t,n){return function(){Y(this,t).duration=+n.apply(this,arguments)}}function rn(t,n){return n=+n,function(){Y(this,t).duration=n}}function on(t,n){if("function"!==typeof n)throw new Error;return function(){Y(this,t).ease=n}}var un=e(51761);function an(t,n,e){var r,o,i=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t}))}(n)?G:Y;return function(){var u=i(this,t),a=u.on;a!==r&&(o=(r=a).copy()).on(n,e),u.on=o}}var cn=e(12538);var ln=e(84265);var fn=w.ZP.prototype.constructor;var sn=e(20946);function hn(t){return function(){this.style.removeProperty(t)}}function pn(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}function dn(t,n,e){var r,o;function i(){var i=n.apply(this,arguments);return i!==o&&(r=(o=i)&&pn(t,i,e)),r}return i._value=n,i}function vn(t){return function(n){this.textContent=t.call(this,n)}}function gn(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&vn(r)),n}return r._value=t,r}var mn=0;function yn(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function wn(){return++mn}var _n=w.ZP.prototype;yn.prototype=function(t){return(0,w.ZP)().transition(t)}.prototype=(0,X.Z)({constructor:yn,select:function(t){var n=this._name,e=this._id;"function"!==typeof t&&(t=(0,cn.Z)(t));for(var r=this._groups,o=r.length,i=new Array(o),u=0;u<o;++u)for(var a,c,l=r[u],f=l.length,s=i[u]=new Array(f),h=0;h<f;++h)(a=l[h])&&(c=t.call(a,a.__data__,h,l))&&("__data__"in a&&(c.__data__=a.__data__),s[h]=c,q(s[h],n,e,h,s,D(a,e)));return new yn(i,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!==typeof t&&(t=(0,ln.Z)(t));for(var r=this._groups,o=r.length,i=[],u=[],a=0;a<o;++a)for(var c,l=r[a],f=l.length,s=0;s<f;++s)if(c=l[s]){for(var h,p=t.call(c,c.__data__,s,l),d=D(c,e),v=0,g=p.length;v<g;++v)(h=p[v])&&q(h,n,e,v,p,d);i.push(p),u.push(c)}return new yn(i,u,n,e)},filter:function(t){"function"!==typeof t&&(t=(0,un.Z)(t));for(var n=this._groups,e=n.length,r=new Array(e),o=0;o<e;++o)for(var i,u=n[o],a=u.length,c=r[o]=[],l=0;l<a;++l)(i=u[l])&&t.call(i,i.__data__,l,u)&&c.push(i);return new yn(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,o=e.length,i=Math.min(r,o),u=new Array(r),a=0;a<i;++a)for(var c,l=n[a],f=e[a],s=l.length,h=u[a]=new Array(s),p=0;p<s;++p)(c=l[p]||f[p])&&(h[p]=c);for(;a<r;++a)u[a]=n[a];return new yn(u,this._parents,this._name,this._id)},selection:function(){return new fn(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=wn(),r=this._groups,o=r.length,i=0;i<o;++i)for(var u,a=r[i],c=a.length,l=0;l<c;++l)if(u=a[l]){var f=D(u,n);q(u,t,e,l,a,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new yn(r,this._parents,t,e)},call:_n.call,nodes:_n.nodes,node:_n.node,size:_n.size,empty:_n.empty,each:_n.each,on:function(t,n){var e=this._id;return arguments.length<2?D(this.node(),e).on.on(t):this.each(an(e,t,n))},attr:function(t,n){var e=(0,J.Z)(t),r="transform"===e?$:Xt;return this.attrTween(t,"function"===typeof n?(e.local?Wt:Vt)(e,r,nt(this,"attr."+t,n)):null==n?(e.local?Bt:Ft)(e):(e.local?Ht:Ut)(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!==typeof n)throw new Error;var r=(0,J.Z)(t);return this.tween(e,(r.local?Jt:Qt)(r,n))},style:function(t,n,e){var r="transform"===(t+="")?K:Xt;return null==n?this.styleTween(t,function(t,n){var e,r,o;return function(){var i=(0,sn.S)(this,t),u=(this.style.removeProperty(t),(0,sn.S)(this,t));return i===u?null:i===e&&u===r?o:o=n(e=i,r=u)}}(t,r)).on("end.style."+t,hn(t)):"function"===typeof n?this.styleTween(t,function(t,n,e){var r,o,i;return function(){var u=(0,sn.S)(this,t),a=e(this),c=a+"";return null==a&&(this.style.removeProperty(t),c=a=(0,sn.S)(this,t)),u===c?null:u===r&&c===o?i:(o=c,i=n(r=u,a))}}(t,r,nt(this,"style."+t,n))).each(function(t,n){var e,r,o,i,u="style."+n,a="end."+u;return function(){var c=Y(this,t),l=c.on,f=null==c.value[u]?i||(i=hn(n)):void 0;l===e&&o===f||(r=(e=l).copy()).on(a,o=f),c.on=r}}(this._id,t)):this.styleTween(t,function(t,n,e){var r,o,i=e+"";return function(){var u=(0,sn.S)(this,t);return u===i?null:u===r?o:o=n(r=u,e)}}(t,r,n),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!==typeof n)throw new Error;return this.tween(r,dn(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"===typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(nt(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!==typeof t)throw new Error;return this.tween(n,gn(t))},remove:function(){return this.on("end.remove",function(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}(this._id))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,o=D(this.node(),e).tween,i=0,u=o.length;i<u;++i)if((r=o[i]).name===t)return r.value;return null}return this.each((null==n?Q:tt)(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"===typeof t?tn:nn)(n,t)):D(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"===typeof t?en:rn)(n,t)):D(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(on(n,t)):D(this.node(),n).ease},easeVarying:function(t){if("function"!==typeof t)throw new Error;return this.each(function(t,n){return function(){var e=n.apply(this,arguments);if("function"!==typeof e)throw new Error;Y(this,t).ease=e}}(this._id,t))},end:function(){var t,n,e=this,r=e._id,o=e.size();return new Promise((function(i,u){var a={value:u},c={value:function(){0===--o&&i()}};e.each((function(){var e=Y(this,r),o=e.on;o!==t&&((n=(t=o).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),e.on=n})),0===o&&i()}))}},Symbol.iterator,_n[Symbol.iterator]);var bn={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function En(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error("transition ".concat(n," not found"));return e}w.ZP.prototype.interrupt=function(t){return this.each((function(){I(this,t)}))},w.ZP.prototype.transition=function(t){var n,e;t instanceof yn?(n=t._id,t=t._name):(n=wn(),(e=bn).time=j(),t=null==t?null:t+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var u,a=r[i],c=a.length,l=0;l<c;++l)(u=a[l])&&q(u,t,n,l,a,e||En(u,n));return new yn(r,this._parents,t,n)};var xn=function(t){return function(){return t}};function Mn(t,n){var e=n.sourceEvent,r=n.target,o=n.transform,i=n.dispatch;Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:i}})}function Sn(t,n,e){this.k=t,this.x=n,this.y=e}Sn.prototype={constructor:Sn,scale:function(t){return 1===t?this:new Sn(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new Sn(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var kn=new Sn(1,0,0);function Nn(t){for(;!t.__zoom;)if(!(t=t.parentNode))return kn;return t.__zoom}function jn(t){t.stopImmediatePropagation()}function Pn(t){t.preventDefault(),t.stopImmediatePropagation()}function An(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function Zn(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function zn(){return this.__zoom||kn}function On(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function Cn(){return navigator.maxTouchPoints||"ontouchstart"in this}function Tn(t,n,e){var r=t.invertX(n[0][0])-e[0][0],o=t.invertX(n[1][0])-e[1][0],i=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),u>i?(i+u)/2:Math.min(0,i)||Math.max(0,u))}function Ln(){var t,n,e,r=An,o=Zn,i=Tn,u=On,a=Cn,c=[0,1/0],s=[[-1/0,-1/0],[1/0,1/0]],d=250,v=m,g=l("start","zoom","end"),w=500,_=0,b=10;function E(t){t.property("__zoom",zn).on("wheel.zoom",P).on("mousedown.zoom",A).on("dblclick.zoom",Z).filter(a).on("touchstart.zoom",z).on("touchmove.zoom",O).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function x(t,n){return(n=Math.max(c[0],Math.min(c[1],n)))===t.k?t:new Sn(n,t.x,t.y)}function M(t,n,e){var r=n[0]-e[0]*t.k,o=n[1]-e[1]*t.k;return r===t.x&&o===t.y?t:new Sn(t.k,r,o)}function S(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function k(t,n,e,r){t.on("start.zoom",(function(){N(this,arguments).event(r).start()})).on("interrupt.zoom end.zoom",(function(){N(this,arguments).event(r).end()})).tween("zoom",(function(){var t=this,i=arguments,u=N(t,i).event(r),a=o.apply(t,i),c=null==e?S(a):"function"===typeof e?e.apply(t,i):e,l=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),f=t.__zoom,s="function"===typeof n?n.apply(t,i):n,h=v(f.invert(c).concat(l/f.k),s.invert(c).concat(l/s.k));return function(t){if(1===t)t=s;else{var n=h(t),e=l/n[2];t=new Sn(e,c[0]-n[0]*e,c[1]-n[1]*e)}u.zoom(null,t)}}))}function N(t,n,e){return!e&&t.__zooming||new j(t,n)}function j(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=o.apply(t,n),this.taps=0}function P(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),o=1;o<n;o++)e[o-1]=arguments[o];if(r.apply(this,arguments)){var a=N(this,e).event(t),l=this.__zoom,f=Math.max(c[0],Math.min(c[1],l.k*Math.pow(2,u.apply(this,arguments)))),h=(0,y.Z)(t);if(a.wheel)a.mouse[0][0]===h[0]&&a.mouse[0][1]===h[1]||(a.mouse[1]=l.invert(a.mouse[0]=h)),clearTimeout(a.wheel);else{if(l.k===f)return;a.mouse=[h,l.invert(h)],I(this),a.start()}Pn(t),a.wheel=setTimeout(p,150),a.zoom("mouse",i(M(x(l,f),a.mouse[0],a.mouse[1]),a.extent,s))}function p(){a.wheel=null,a.end()}}function A(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),u=1;u<n;u++)o[u-1]=arguments[u];if(!e&&r.apply(this,arguments)){var a=N(this,o,!0).event(t),c=(0,f.Z)(t.view).on("mousemove.zoom",m,!0).on("mouseup.zoom",w,!0),l=(0,y.Z)(t,d),d=t.currentTarget,v=t.clientX,g=t.clientY;h(t.view),jn(t),a.mouse=[l,this.__zoom.invert(l)],I(this),a.start()}function m(t){if(Pn(t),!a.moved){var n=t.clientX-v,e=t.clientY-g;a.moved=n*n+e*e>_}a.event(t).zoom("mouse",i(M(a.that.__zoom,a.mouse[0]=(0,y.Z)(t,d),a.mouse[1]),a.extent,s))}function w(t){c.on("mousemove.zoom mouseup.zoom",null),p(t.view,a.moved),Pn(t),a.event(t).end()}}function Z(t){for(var n=arguments.length,e=new Array(n>1?n-1:0),u=1;u<n;u++)e[u-1]=arguments[u];if(r.apply(this,arguments)){var a=this.__zoom,c=(0,y.Z)(t.changedTouches?t.changedTouches[0]:t,this),l=a.invert(c),h=a.k*(t.shiftKey?.5:2),p=i(M(x(a,h),c,l),o.apply(this,e),s);Pn(t),d>0?(0,f.Z)(this).transition().duration(d).call(k,p,c,t):(0,f.Z)(this).call(E.transform,p,c,t)}}function z(e){for(var o=arguments.length,i=new Array(o>1?o-1:0),u=1;u<o;u++)i[u-1]=arguments[u];if(r.apply(this,arguments)){var a,c,l,f,s=e.touches,h=s.length,p=N(this,i,e.changedTouches.length===h).event(e);for(jn(e),c=0;c<h;++c)l=s[c],f=[f=(0,y.Z)(l,this),this.__zoom.invert(f),l.identifier],p.touch0?p.touch1||p.touch0[2]===f[2]||(p.touch1=f,p.taps=0):(p.touch0=f,a=!0,p.taps=1+!!t);t&&(t=clearTimeout(t)),a&&(p.taps<2&&(n=f[0],t=setTimeout((function(){t=null}),w)),I(this),p.start())}}function O(t){if(this.__zooming){for(var n=arguments.length,e=new Array(n>1?n-1:0),r=1;r<n;r++)e[r-1]=arguments[r];var o,u,a,c,l=N(this,e).event(t),f=t.changedTouches,h=f.length;for(Pn(t),o=0;o<h;++o)u=f[o],a=(0,y.Z)(u,this),l.touch0&&l.touch0[2]===u.identifier?l.touch0[0]=a:l.touch1&&l.touch1[2]===u.identifier&&(l.touch1[0]=a);if(u=l.that.__zoom,l.touch1){var p=l.touch0[0],d=l.touch0[1],v=l.touch1[0],g=l.touch1[1],m=(m=v[0]-p[0])*m+(m=v[1]-p[1])*m,w=(w=g[0]-d[0])*w+(w=g[1]-d[1])*w;u=x(u,Math.sqrt(m/w)),a=[(p[0]+v[0])/2,(p[1]+v[1])/2],c=[(d[0]+g[0])/2,(d[1]+g[1])/2]}else{if(!l.touch0)return;a=l.touch0[0],c=l.touch0[1]}l.zoom("touch",i(M(u,a,c),l.extent,s))}}function C(t){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];if(this.__zooming){var u,a,c=N(this,o).event(t),l=t.changedTouches,s=l.length;for(jn(t),e&&clearTimeout(e),e=setTimeout((function(){e=null}),w),u=0;u<s;++u)a=l[u],c.touch0&&c.touch0[2]===a.identifier?delete c.touch0:c.touch1&&c.touch1[2]===a.identifier&&delete c.touch1;if(c.touch1&&!c.touch0&&(c.touch0=c.touch1,delete c.touch1),c.touch0)c.touch0[1]=this.__zoom.invert(c.touch0[0]);else if(c.end(),2===c.taps&&(a=(0,y.Z)(a,this),Math.hypot(n[0]-a[0],n[1]-a[1])<b)){var h=(0,f.Z)(this).on("dblclick.zoom");h&&h.apply(this,arguments)}}}return E.transform=function(t,n,e,r){var o=t.selection?t.selection():t;o.property("__zoom",zn),t!==o?k(t,n,e,r):o.interrupt().each((function(){N(this,arguments).event(r).start().zoom(null,"function"===typeof n?n.apply(this,arguments):n).end()}))},E.scaleBy=function(t,n,e,r){E.scaleTo(t,(function(){var t=this.__zoom.k,e="function"===typeof n?n.apply(this,arguments):n;return t*e}),e,r)},E.scaleTo=function(t,n,e,r){E.transform(t,(function(){var t=o.apply(this,arguments),r=this.__zoom,u=null==e?S(t):"function"===typeof e?e.apply(this,arguments):e,a=r.invert(u),c="function"===typeof n?n.apply(this,arguments):n;return i(M(x(r,c),u,a),t,s)}),e,r)},E.translateBy=function(t,n,e,r){E.transform(t,(function(){return i(this.__zoom.translate("function"===typeof n?n.apply(this,arguments):n,"function"===typeof e?e.apply(this,arguments):e),o.apply(this,arguments),s)}),null,r)},E.translateTo=function(t,n,e,r,u){E.transform(t,(function(){var t=o.apply(this,arguments),u=this.__zoom,a=null==r?S(t):"function"===typeof r?r.apply(this,arguments):r;return i(kn.translate(a[0],a[1]).scale(u.k).translate("function"===typeof n?-n.apply(this,arguments):-n,"function"===typeof e?-e.apply(this,arguments):-e),t,s)}),r,u)},j.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1===++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0===--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=(0,f.Z)(this.that).datum();g.call(t,this.that,new Mn(t,{sourceEvent:this.sourceEvent,target:E,type:t,transform:this.that.__zoom,dispatch:g}),n)}},E.wheelDelta=function(t){return arguments.length?(u="function"===typeof t?t:xn(+t),E):u},E.filter=function(t){return arguments.length?(r="function"===typeof t?t:xn(!!t),E):r},E.touchable=function(t){return arguments.length?(a="function"===typeof t?t:xn(!!t),E):a},E.extent=function(t){return arguments.length?(o="function"===typeof t?t:xn([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),E):o},E.scaleExtent=function(t){return arguments.length?(c[0]=+t[0],c[1]=+t[1],E):[c[0],c[1]]},E.translateExtent=function(t){return arguments.length?(s[0][0]=+t[0][0],s[1][0]=+t[1][0],s[0][1]=+t[0][1],s[1][1]=+t[1][1],E):[[s[0][0],s[0][1]],[s[1][0],s[1][1]]]},E.constrain=function(t){return arguments.length?(i=t,E):i},E.duration=function(t){return arguments.length?(d=+t,E):d},E.interpolate=function(t){return arguments.length?(v=t,E):v},E.on=function(){var t=g.on.apply(g,arguments);return t===g?E:t},E.clickDistance=function(t){return arguments.length?(_=(t=+t)*t,E):Math.sqrt(_)},E.tapDistance=function(t){return arguments.length?(b=+t,E):b},E}Nn.prototype=Sn.prototype},9025:function(t,n,e){!function(t,n,e,r,o,i,u){"use strict";function a(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}function c(t){if(t&&t.__esModule)return t;var n=Object.create(null);return t&&Object.keys(t).forEach((function(e){if("default"!==e){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(n,e,r.get?r:{enumerable:!0,get:function(){return t[e]}})}})),n.default=t,Object.freeze(n)}var l=a(n),f=a(e),s=c(r);function h(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function p(t){for(var n=1;n<arguments.length;n++){var e=null!=arguments[n]?arguments[n]:{};n%2?h(Object(e),!0).forEach((function(n){v(t,n,e[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):h(Object(e)).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))}))}return t}function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function v(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var n=1;n<arguments.length;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t},g.apply(this,arguments)}function m(t,n){if(null==t)return{};var e,r,o=function(t,n){if(null==t)return{};var e,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)e=i[r],n.indexOf(e)>=0||(o[e]=t[e]);return o}(t,n);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)e=i[r],n.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(t,e)&&(o[e]=t[e])}return o}function y(t,n){return function(t){if(Array.isArray(t))return t}(t)||function(t,n){var e=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var r,o,i=[],u=!0,a=!1;try{for(e=e.call(t);!(u=(r=e.next()).done)&&(i.push(r.value),!n||i.length!==n);u=!0);}catch(t){a=!0,o=t}finally{try{u||null==e.return||e.return()}finally{if(a)throw o}}return i}}(t,n)||function(t,n){if(t){if("string"==typeof t)return w(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?w(t,n):void 0}}(t,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}var _=["width","height","projection","projectionConfig"],b=s.geoPath,E=m(s,["geoPath"]),x=n.createContext(),M=function(t){var e=t.width,r=t.height,o=t.projection,i=t.projectionConfig,u=m(t,_),a=y(i.center||[],2),c=a[0],f=a[1],s=y(i.rotate||[],3),h=s[0],p=s[1],d=s[2],v=y(i.parallels||[],2),w=v[0],M=v[1],S=i.scale||null,k=n.useMemo((function(){return function(t){var n=t.projectionConfig,e=void 0===n?{}:n,r=t.projection,o=void 0===r?"geoEqualEarth":r,i=t.width,u=void 0===i?800:i,a=t.height,c=void 0===a?600:a;if("function"==typeof o)return o;var l=E[o]().translate([u/2,c/2]);return[l.center?"center":null,l.rotate?"rotate":null,l.scale?"scale":null,l.parallels?"parallels":null].forEach((function(t){t&&(l=l[t](e[t]||l[t]()))})),l}({projectionConfig:{center:c||0===c||f||0===f?[c,f]:null,rotate:h||0===h||p||0===p?[h,p,d]:null,parallels:w||0===w||M||0===M?[w,M]:null,scale:S},projection:o,width:e,height:r})}),[e,r,o,c,f,h,p,d,w,M,S]),N=n.useCallback(k,[k]),j=n.useMemo((function(){return{width:e,height:r,projection:N,path:b().projection(N)}}),[e,r,N]);return l.default.createElement(x.Provider,g({value:j},u))};M.propTypes={width:f.default.number,height:f.default.number,projection:f.default.oneOfType([f.default.string,f.default.func]),projectionConfig:f.default.object};var S=["width","height","projection","projectionConfig","className"],k=n.forwardRef((function(t,n){var e=t.width,r=void 0===e?800:e,o=t.height,i=void 0===o?600:o,u=t.projection,a=void 0===u?"geoEqualEarth":u,c=t.projectionConfig,f=void 0===c?{}:c,s=t.className,h=void 0===s?"":s,p=m(t,S);return l.default.createElement(M,{width:r,height:i,projection:a,projectionConfig:f},l.default.createElement("svg",g({ref:n,viewBox:"0 0 ".concat(r," ").concat(i),className:"rsm-svg ".concat(h)},p)))}));function N(t,n,e){var r=(t*e.k-t)/2,o=(n*e.k-n)/2;return[t/2-(r+e.x)/e.k,n/2-(o+e.y)/e.k]}function j(t,n){if("Topology"!==t.type)return n?n(t.features||t):t.features||t;var e=o.feature(t,t.objects[Object.keys(t.objects)[0]]).features;return n?n(e):e}function P(t){return"Topology"===t.type?{outline:o.mesh(t,t.objects[Object.keys(t.objects)[0]],(function(t,n){return t===n})),borders:o.mesh(t,t.objects[Object.keys(t.objects)[0]],(function(t,n){return t!==n}))}:null}function A(t,n){return t?t.map((function(t,e){return p(p({},t),{},{rsmKey:"geo-".concat(e),svgPath:n(t)})})):[]}function Z(t){var e=t.geography,r=t.parseGeographies,o=n.useContext(x).path,i=y(n.useState({}),2),u=i[0],a=i[1];n.useEffect((function(){var t;"undefined"!==("undefined"==typeof window?"undefined":d(window))&&e&&("string"==typeof e?(t=e,fetch(t).then((function(t){if(!t.ok)throw Error(t.statusText);return t.json()})).catch((function(t){console.log("There was a problem when fetching the data: ",t)}))).then((function(t){t&&a({geographies:j(t,r),mesh:P(t)})})):a({geographies:j(e,r),mesh:P(e)}))}),[e,r]);var c=n.useMemo((function(){var t=u.mesh||{},n=function(t,n,e){return t&&n?{outline:p(p({},t),{},{rsmKey:"outline",svgPath:e(t)}),borders:p(p({},n),{},{rsmKey:"borders",svgPath:e(n)})}:{}}(t.outline,t.borders,o);return{geographies:A(u.geographies,o),outline:n.outline,borders:n.borders}}),[u,o]);return{geographies:c.geographies,outline:c.outline,borders:c.borders}}k.displayName="ComposableMap",k.propTypes={width:f.default.number,height:f.default.number,projection:f.default.oneOfType([f.default.string,f.default.func]),projectionConfig:f.default.object,className:f.default.string};var z=["geography","children","parseGeographies","className"],O=n.forwardRef((function(t,e){var r=t.geography,o=t.children,i=t.parseGeographies,u=t.className,a=void 0===u?"":u,c=m(t,z),f=n.useContext(x),s=f.path,h=f.projection,p=Z({geography:r,parseGeographies:i}),d=p.geographies,v=p.outline,y=p.borders;return l.default.createElement("g",g({ref:e,className:"rsm-geographies ".concat(a)},c),d&&d.length>0&&o({geographies:d,outline:v,borders:y,path:s,projection:h}))}));O.displayName="Geographies",O.propTypes={geography:f.default.oneOfType([f.default.string,f.default.object,f.default.array]),children:f.default.func,parseGeographies:f.default.func,className:f.default.string};var C=["geography","onMouseEnter","onMouseLeave","onMouseDown","onMouseUp","onFocus","onBlur","style","className"],T=n.forwardRef((function(t,e){var r=t.geography,o=t.onMouseEnter,i=t.onMouseLeave,u=t.onMouseDown,a=t.onMouseUp,c=t.onFocus,f=t.onBlur,s=t.style,h=void 0===s?{}:s,p=t.className,d=void 0===p?"":p,v=m(t,C),w=y(n.useState(!1),2),_=w[0],b=w[1],E=y(n.useState(!1),2),x=E[0],M=E[1];return l.default.createElement("path",g({ref:e,tabIndex:"0",className:"rsm-geography ".concat(d),d:r.svgPath,onMouseEnter:function(t){M(!0),o&&o(t)},onMouseLeave:function(t){M(!1),_&&b(!1),i&&i(t)},onFocus:function(t){M(!0),c&&c(t)},onBlur:function(t){M(!1),_&&b(!1),f&&f(t)},onMouseDown:function(t){b(!0),u&&u(t)},onMouseUp:function(t){b(!1),a&&a(t)},style:h[_||x?_?"pressed":"hover":"default"]},v))}));T.displayName="Geography",T.propTypes={geography:f.default.object,onMouseEnter:f.default.func,onMouseLeave:f.default.func,onMouseDown:f.default.func,onMouseUp:f.default.func,onFocus:f.default.func,onBlur:f.default.func,style:f.default.object,className:f.default.string};var L=n.memo(T),R=["fill","stroke","step","className"],q=n.forwardRef((function(t,e){var o=t.fill,i=void 0===o?"transparent":o,u=t.stroke,a=void 0===u?"currentcolor":u,c=t.step,f=void 0===c?[10,10]:c,s=t.className,h=void 0===s?"":s,p=m(t,R),d=n.useContext(x).path;return l.default.createElement("path",g({ref:e,d:d(r.geoGraticule().step(f)()),fill:i,stroke:a,className:"rsm-graticule ".concat(h)},p))}));q.displayName="Graticule",q.propTypes={fill:f.default.string,stroke:f.default.string,step:f.default.array,className:f.default.string};var G=n.memo(q),Y=["value"],D=n.createContext(),I={x:0,y:0,k:1,transformString:"translate(0 0) scale(1)"},X=function(t){var n=t.value,e=void 0===n?I:n,r=m(t,Y);return l.default.createElement(D.Provider,g({value:e},r))};function F(t){var e=t.center,r=t.filterZoomEvent,o=t.onMoveStart,a=t.onMoveEnd,c=t.onMove,l=t.translateExtent,f=void 0===l?[[-1/0,-1/0],[1/0,1/0]]:l,s=t.scaleExtent,h=void 0===s?[1,8]:s,p=t.zoom,d=void 0===p?1:p,v=n.useContext(x),g=v.width,m=v.height,w=v.projection,_=y(e,2),b=_[0],E=_[1],M=y(n.useState({x:0,y:0,k:1}),2),S=M[0],k=M[1],j=n.useRef({x:0,y:0,k:1}),P=n.useRef(),A=n.useRef(),Z=n.useRef(!1),z=y(f,2),O=z[0],C=z[1],T=y(O,2),L=T[0],R=T[1],q=y(C,2),G=q[0],Y=q[1],D=y(h,2),I=D[0],X=D[1];return n.useEffect((function(){var t=u.select(P.current),n=i.zoom().filter((function(t){return r?r(t):!!t&&!t.ctrlKey&&!t.button})).scaleExtent([I,X]).translateExtent([[L,R],[G,Y]]).on("start",(function(t){o&&!Z.current&&o({coordinates:w.invert(N(g,m,t.transform)),zoom:t.transform.k},t)})).on("zoom",(function(t){if(!Z.current){var n=t.transform,e=t.sourceEvent;k({x:n.x,y:n.y,k:n.k,dragging:e}),c&&c({x:n.x,y:n.y,zoom:n.k,dragging:e},t)}})).on("end",(function(t){if(Z.current)Z.current=!1;else{var n=y(w.invert(N(g,m,t.transform)),2),e=n[0],r=n[1];j.current={x:e,y:r,k:t.transform.k},a&&a({coordinates:[e,r],zoom:t.transform.k},t)}}));A.current=n,t.call(n)}),[g,m,L,R,G,Y,I,X,w,o,c,a,r]),n.useEffect((function(){if(b!==j.current.x||E!==j.current.y||d!==j.current.k){var t=w([b,E]),n=t[0]*d,e=t[1]*d,r=u.select(P.current);Z.current=!0,r.call(A.current.transform,i.zoomIdentity.translate(g/2-n,m/2-e).scale(d)),k({x:g/2-n,y:m/2-e,k:d}),j.current={x:b,y:E,k:d}}}),[b,E,d,g,m,w]),{mapRef:P,position:S,transformString:"translate(".concat(S.x," ").concat(S.y,") scale(").concat(S.k,")")}}X.propTypes={x:f.default.number,y:f.default.number,k:f.default.number,transformString:f.default.string};var B=["center","zoom","minZoom","maxZoom","translateExtent","filterZoomEvent","onMoveStart","onMove","onMoveEnd","className"],U=n.forwardRef((function(t,e){var r=t.center,o=void 0===r?[0,0]:r,i=t.zoom,u=void 0===i?1:i,a=t.minZoom,c=void 0===a?1:a,f=t.maxZoom,s=void 0===f?8:f,h=t.translateExtent,p=t.filterZoomEvent,d=t.onMoveStart,v=t.onMove,y=t.onMoveEnd,w=t.className,_=m(t,B),b=n.useContext(x),E=b.width,M=b.height,S=F({center:o,filterZoomEvent:p,onMoveStart:d,onMove:v,onMoveEnd:y,scaleExtent:[c,s],translateExtent:h,zoom:u}),k=S.mapRef,N=S.transformString,j=S.position;return l.default.createElement(X,{value:{x:j.x,y:j.y,k:j.k,transformString:N}},l.default.createElement("g",{ref:k},l.default.createElement("rect",{width:E,height:M,fill:"transparent"}),l.default.createElement("g",g({ref:e,transform:N,className:"rsm-zoomable-group ".concat(w)},_))))}));U.displayName="ZoomableGroup",U.propTypes={center:f.default.array,zoom:f.default.number,minZoom:f.default.number,maxZoom:f.default.number,translateExtent:f.default.arrayOf(f.default.array),onMoveStart:f.default.func,onMove:f.default.func,onMoveEnd:f.default.func,className:f.default.string};var H=["id","fill","stroke","strokeWidth","className"],V=n.forwardRef((function(t,e){var r=t.id,o=void 0===r?"rsm-sphere":r,i=t.fill,u=void 0===i?"transparent":i,a=t.stroke,c=void 0===a?"currentcolor":a,f=t.strokeWidth,s=void 0===f?.5:f,h=t.className,p=void 0===h?"":h,d=m(t,H),v=n.useContext(x).path,y=n.useMemo((function(){return v({type:"Sphere"})}),[v]);return l.default.createElement(n.Fragment,null,l.default.createElement("defs",null,l.default.createElement("clipPath",{id:o},l.default.createElement("path",{d:y}))),l.default.createElement("path",g({ref:e,d:y,fill:u,stroke:c,strokeWidth:s,style:{pointerEvents:"none"},className:"rsm-sphere ".concat(p)},d)))}));V.displayName="Sphere",V.propTypes={id:f.default.string,fill:f.default.string,stroke:f.default.string,strokeWidth:f.default.number,className:f.default.string};var W=n.memo(V),K=["coordinates","children","onMouseEnter","onMouseLeave","onMouseDown","onMouseUp","onFocus","onBlur","style","className"],$=n.forwardRef((function(t,e){var r=t.coordinates,o=t.children,i=t.onMouseEnter,u=t.onMouseLeave,a=t.onMouseDown,c=t.onMouseUp,f=t.onFocus,s=t.onBlur,h=t.style,p=void 0===h?{}:h,d=t.className,v=void 0===d?"":d,w=m(t,K),_=n.useContext(x).projection,b=y(n.useState(!1),2),E=b[0],M=b[1],S=y(n.useState(!1),2),k=S[0],N=S[1],j=y(_(r),2),P=j[0],A=j[1];return l.default.createElement("g",g({ref:e,transform:"translate(".concat(P,", ").concat(A,")"),className:"rsm-marker ".concat(v),onMouseEnter:function(t){N(!0),i&&i(t)},onMouseLeave:function(t){N(!1),E&&M(!1),u&&u(t)},onFocus:function(t){N(!0),f&&f(t)},onBlur:function(t){N(!1),E&&M(!1),s&&s(t)},onMouseDown:function(t){M(!0),a&&a(t)},onMouseUp:function(t){M(!1),c&&c(t)},style:p[E||k?E?"pressed":"hover":"default"]},w),o)}));$.displayName="Marker",$.propTypes={coordinates:f.default.array,children:f.default.oneOfType([f.default.node,f.default.arrayOf(f.default.node)]),onMouseEnter:f.default.func,onMouseLeave:f.default.func,onMouseDown:f.default.func,onMouseUp:f.default.func,onFocus:f.default.func,onBlur:f.default.func,style:f.default.object,className:f.default.string};var J=["from","to","coordinates","stroke","strokeWidth","fill","className"],Q=n.forwardRef((function(t,e){var r=t.from,o=void 0===r?[0,0]:r,i=t.to,u=void 0===i?[0,0]:i,a=t.coordinates,c=t.stroke,f=void 0===c?"currentcolor":c,s=t.strokeWidth,h=void 0===s?3:s,p=t.fill,d=void 0===p?"transparent":p,v=t.className,y=void 0===v?"":v,w=m(t,J),_=n.useContext(x).path,b={type:"LineString",coordinates:a||[o,u]};return l.default.createElement("path",g({ref:e,d:_(b),className:"rsm-line ".concat(y),stroke:f,strokeWidth:h,fill:d},w))}));Q.displayName="Line",Q.propTypes={from:f.default.array,to:f.default.array,coordinates:f.default.array,stroke:f.default.string,strokeWidth:f.default.number,fill:f.default.string,className:f.default.string};var tt=["subject","children","connectorProps","dx","dy","curve","className"],nt=n.forwardRef((function(t,e){var r=t.subject,o=t.children,i=t.connectorProps,u=t.dx,a=void 0===u?30:u,c=t.dy,f=void 0===c?30:c,s=t.curve,h=void 0===s?0:s,p=t.className,d=void 0===p?"":p,v=m(t,tt),w=y((0,n.useContext(x).projection)(r),2),_=w[0],b=w[1],E=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.5,r=Array.isArray(e)?e:[e,e],o=t/2*r[0],i=n/2*r[1];return"M".concat(0,",",0," Q",-t/2-o,",").concat(-n/2+i," ").concat(-t,",").concat(-n)}(a,f,h);return l.default.createElement("g",g({ref:e,transform:"translate(".concat(_+a,", ").concat(b+f,")"),className:"rsm-annotation ".concat(d)},v),l.default.createElement("path",g({d:E,fill:"transparent",stroke:"#000"},i)),o)}));nt.displayName="Annotation",nt.propTypes={subject:f.default.array,children:f.default.oneOfType([f.default.node,f.default.arrayOf(f.default.node)]),dx:f.default.number,dy:f.default.number,curve:f.default.number,connectorProps:f.default.object,className:f.default.string},t.Annotation=nt,t.ComposableMap=k,t.Geographies=O,t.Geography=L,t.Graticule=G,t.Line=Q,t.MapContext=x,t.MapProvider=M,t.Marker=$,t.Sphere=W,t.ZoomPanContext=D,t.ZoomPanProvider=X,t.ZoomableGroup=U,t.useGeographies=Z,t.useMapContext=function(){return n.useContext(x)},t.useZoomPan=F,t.useZoomPanContext=function(){return n.useContext(D)},Object.defineProperty(t,"__esModule",{value:!0})}(n,e(72791),e(52007),e(69958),e(41062),e(39839),e(32320))},41062:function(t,n,e){"use strict";function r(t){return t}function o(t){if(null==t)return r;var n,e,o=t.scale[0],i=t.scale[1],u=t.translate[0],a=t.translate[1];return function(t,r){r||(n=e=0);var c=2,l=t.length,f=new Array(l);for(f[0]=(n+=t[0])*o+u,f[1]=(e+=t[1])*i+a;c<l;)f[c]=t[c],++c;return f}}function i(t){var n,e=o(t.transform),r=1/0,i=r,u=-r,a=-r;function c(t){(t=e(t))[0]<r&&(r=t[0]),t[0]>u&&(u=t[0]),t[1]<i&&(i=t[1]),t[1]>a&&(a=t[1])}function l(t){switch(t.type){case"GeometryCollection":t.geometries.forEach(l);break;case"Point":c(t.coordinates);break;case"MultiPoint":t.coordinates.forEach(c)}}for(n in t.arcs.forEach((function(t){for(var n,o=-1,c=t.length;++o<c;)(n=e(t[o],o))[0]<r&&(r=n[0]),n[0]>u&&(u=n[0]),n[1]<i&&(i=n[1]),n[1]>a&&(a=n[1])})),t.objects)l(t.objects[n]);return[r,i,u,a]}function u(t,n){return"string"===typeof n&&(n=t.objects[n]),"GeometryCollection"===n.type?{type:"FeatureCollection",features:n.geometries.map((function(n){return a(t,n)}))}:a(t,n)}function a(t,n){var e=n.id,r=n.bbox,o=null==n.properties?{}:n.properties,i=c(t,n);return null==e&&null==r?{type:"Feature",properties:o,geometry:i}:null==r?{type:"Feature",id:e,properties:o,geometry:i}:{type:"Feature",id:e,bbox:r,properties:o,geometry:i}}function c(t,n){var e=o(t.transform),r=t.arcs;function i(t,n){n.length&&n.pop();for(var o=r[t<0?~t:t],i=0,u=o.length;i<u;++i)n.push(e(o[i],i));t<0&&function(t,n){for(var e,r=t.length,o=r-n;o<--r;)e=t[o],t[o++]=t[r],t[r]=e}(n,u)}function u(t){return e(t)}function a(t){for(var n=[],e=0,r=t.length;e<r;++e)i(t[e],n);return n.length<2&&n.push(n[0]),n}function c(t){for(var n=a(t);n.length<4;)n.push(n[0]);return n}function l(t){return t.map(c)}return function t(n){var e,r=n.type;switch(r){case"GeometryCollection":return{type:r,geometries:n.geometries.map(t)};case"Point":e=u(n.coordinates);break;case"MultiPoint":e=n.coordinates.map(u);break;case"LineString":e=a(n.arcs);break;case"MultiLineString":e=n.arcs.map(a);break;case"Polygon":e=l(n.arcs);break;case"MultiPolygon":e=n.arcs.map(l);break;default:return null}return{type:r,coordinates:e}}(n)}function l(t,n){var e={},r={},o={},i=[],u=-1;function a(t,n){for(var r in t){var o=t[r];delete n[o.start],delete o.start,delete o.end,o.forEach((function(t){e[t<0?~t:t]=1})),i.push(o)}}return n.forEach((function(e,r){var o,i=t.arcs[e<0?~e:e];i.length<3&&!i[1][0]&&!i[1][1]&&(o=n[++u],n[u]=e,n[r]=o)})),n.forEach((function(n){var e,i,u=function(n){var e,r=t.arcs[n<0?~n:n],o=r[0];t.transform?(e=[0,0],r.forEach((function(t){e[0]+=t[0],e[1]+=t[1]}))):e=r[r.length-1];return n<0?[e,o]:[o,e]}(n),a=u[0],c=u[1];if(e=o[a])if(delete o[e.end],e.push(n),e.end=c,i=r[c]){delete r[i.start];var l=i===e?e:e.concat(i);r[l.start=e.start]=o[l.end=i.end]=l}else r[e.start]=o[e.end]=e;else if(e=r[c])if(delete r[e.start],e.unshift(n),e.start=a,i=o[a]){delete o[i.end];var f=i===e?e:i.concat(e);r[f.start=i.start]=o[f.end=e.end]=f}else r[e.start]=o[e.end]=e;else r[(e=[n]).start=a]=o[e.end=c]=e})),a(o,r),a(r,o),n.forEach((function(t){e[t<0?~t:t]||i.push([t])})),i}function f(t){return c(t,s.apply(this,arguments))}function s(t,n,e){var r,o,i;if(arguments.length>1)r=h(t,n,e);else for(o=0,r=new Array(i=t.arcs.length);o<i;++o)r[o]=o;return{type:"MultiLineString",arcs:l(t,r)}}function h(t,n,e){var r,o=[],i=[];function u(t){var n=t<0?~t:t;(i[n]||(i[n]=[])).push({i:t,g:r})}function a(t){t.forEach(u)}function c(t){t.forEach(a)}return function t(n){switch(r=n,n.type){case"GeometryCollection":n.geometries.forEach(t);break;case"LineString":a(n.arcs);break;case"MultiLineString":case"Polygon":c(n.arcs);break;case"MultiPolygon":!function(t){t.forEach(c)}(n.arcs)}}(n),i.forEach(null==e?function(t){o.push(t[0].i)}:function(t){e(t[0].g,t[t.length-1].g)&&o.push(t[0].i)}),o}function p(t){return c(t,d.apply(this,arguments))}function d(t,n){var e={},r=[],o=[];function i(t){t.forEach((function(n){n.forEach((function(n){(e[n=n<0?~n:n]||(e[n]=[])).push(t)}))})),r.push(t)}function u(n){return function(t){for(var n,e=-1,r=t.length,o=t[r-1],i=0;++e<r;)n=o,o=t[e],i+=n[0]*o[1]-n[1]*o[0];return Math.abs(i)}(c(t,{type:"Polygon",arcs:[n]}).coordinates[0])}return n.forEach((function t(n){switch(n.type){case"GeometryCollection":n.geometries.forEach(t);break;case"Polygon":i(n.arcs);break;case"MultiPolygon":n.arcs.forEach(i)}})),r.forEach((function(t){if(!t._){var n=[],r=[t];for(t._=1,o.push(n);t=r.pop();)n.push(t),t.forEach((function(t){t.forEach((function(t){e[t<0?~t:t].forEach((function(t){t._||(t._=1,r.push(t))}))}))}))}})),r.forEach((function(t){delete t._})),{type:"MultiPolygon",arcs:o.map((function(n){var r,o=[];if(n.forEach((function(t){t.forEach((function(t){t.forEach((function(t){e[t<0?~t:t].length<2&&o.push(t)}))}))})),(r=(o=l(t,o)).length)>1)for(var i,a,c=1,f=u(o[0]);c<r;++c)(i=u(o[c]))>f&&(a=o[0],o[0]=o[c],o[c]=a,f=i);return o})).filter((function(t){return t.length>0}))}}function v(t,n){for(var e=0,r=t.length;e<r;){var o=e+r>>>1;t[o]<n?e=o+1:r=o}return e}function g(t){var n={},e=t.map((function(){return[]}));function r(t,e){t.forEach((function(t){t<0&&(t=~t);var r=n[t];r?r.push(e):n[t]=[e]}))}function o(t,n){t.forEach((function(t){r(t,n)}))}var i={LineString:r,MultiLineString:o,Polygon:o,MultiPolygon:function(t,n){t.forEach((function(t){o(t,n)}))}};for(var u in t.forEach((function t(n,e){"GeometryCollection"===n.type?n.geometries.forEach((function(n){t(n,e)})):n.type in i&&i[n.type](n.arcs,e)})),n)for(var a=n[u],c=a.length,l=0;l<c;++l)for(var f=l+1;f<c;++f){var s,h=a[l],p=a[f];(s=e[h])[u=v(s,p)]!==p&&s.splice(u,0,p),(s=e[p])[u=v(s,h)]!==h&&s.splice(u,0,h)}return e}function m(t){if(null==t)return r;var n,e,o=t.scale[0],i=t.scale[1],u=t.translate[0],a=t.translate[1];return function(t,r){r||(n=e=0);var c=2,l=t.length,f=new Array(l),s=Math.round((t[0]-u)/o),h=Math.round((t[1]-a)/i);for(f[0]=s-n,n=s,f[1]=h-e,e=h;c<l;)f[c]=t[c],++c;return f}}function y(t,n){if(t.transform)throw new Error("already quantized");if(n&&n.scale)c=t.bbox;else{if(!((e=Math.floor(n))>=2))throw new Error("n must be \u22652");var e,r=(c=t.bbox||i(t))[0],o=c[1],u=c[2],a=c[3];n={scale:[u-r?(u-r)/(e-1):1,a-o?(a-o)/(e-1):1],translate:[r,o]}}var c,l,f=m(n),s=t.objects,h={};function p(t){return f(t)}function d(t){var n;switch(t.type){case"GeometryCollection":n={type:"GeometryCollection",geometries:t.geometries.map(d)};break;case"Point":n={type:"Point",coordinates:p(t.coordinates)};break;case"MultiPoint":n={type:"MultiPoint",coordinates:t.coordinates.map(p)};break;default:return t}return null!=t.id&&(n.id=t.id),null!=t.bbox&&(n.bbox=t.bbox),null!=t.properties&&(n.properties=t.properties),n}for(l in s)h[l]=d(s[l]);return{type:"Topology",bbox:c,transform:n,objects:h,arcs:t.arcs.map((function(t){var n,e=0,r=1,o=t.length,i=new Array(o);for(i[0]=f(t[0],0);++e<o;)((n=f(t[e],e))[0]||n[1])&&(i[r++]=n);return 1===r&&(i[r++]=[0,0]),i.length=r,i}))}}e.r(n),e.d(n,{bbox:function(){return i},feature:function(){return u},merge:function(){return p},mergeArcs:function(){return d},mesh:function(){return f},meshArcs:function(){return s},neighbors:function(){return g},quantize:function(){return y},transform:function(){return o},untransform:function(){return m}})},74165:function(t,n,e){"use strict";e.d(n,{Z:function(){return o}});var r=e(71002);function o(){o=function(){return t};var t={},n=Object.prototype,e=n.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(t,n,e){return Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[n]}try{l({},"")}catch(j){l=function(t,n,e){return t[n]=e}}function f(t,n,e,r){var o=n&&n.prototype instanceof p?n:p,i=Object.create(o.prototype),u=new S(r||[]);return i._invoke=function(t,n,e){var r="suspendedStart";return function(o,i){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===o)throw i;return N()}for(e.method=o,e.arg=i;;){var u=e.delegate;if(u){var a=E(u,e);if(a){if(a===h)continue;return a}}if("next"===e.method)e.sent=e._sent=e.arg;else if("throw"===e.method){if("suspendedStart"===r)throw r="completed",e.arg;e.dispatchException(e.arg)}else"return"===e.method&&e.abrupt("return",e.arg);r="executing";var c=s(t,n,e);if("normal"===c.type){if(r=e.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:e.done}}"throw"===c.type&&(r="completed",e.method="throw",e.arg=c.arg)}}}(t,e,u),i}function s(t,n,e){try{return{type:"normal",arg:t.call(n,e)}}catch(j){return{type:"throw",arg:j}}}t.wrap=f;var h={};function p(){}function d(){}function v(){}var g={};l(g,u,(function(){return this}));var m=Object.getPrototypeOf,y=m&&m(m(k([])));y&&y!==n&&e.call(y,u)&&(g=y);var w=v.prototype=p.prototype=Object.create(g);function _(t){["next","throw","return"].forEach((function(n){l(t,n,(function(t){return this._invoke(n,t)}))}))}function b(t,n){function o(i,u,a,c){var l=s(t[i],t,u);if("throw"!==l.type){var f=l.arg,h=f.value;return h&&"object"==(0,r.Z)(h)&&e.call(h,"__await")?n.resolve(h.__await).then((function(t){o("next",t,a,c)}),(function(t){o("throw",t,a,c)})):n.resolve(h).then((function(t){f.value=t,a(f)}),(function(t){return o("throw",t,a,c)}))}c(l.arg)}var i;this._invoke=function(t,e){function r(){return new n((function(n,r){o(t,e,n,r)}))}return i=i?i.then(r,r):r()}}function E(t,n){var e=t.iterator[n.method];if(void 0===e){if(n.delegate=null,"throw"===n.method){if(t.iterator.return&&(n.method="return",n.arg=void 0,E(t,n),"throw"===n.method))return h;n.method="throw",n.arg=new TypeError("The iterator does not provide a 'throw' method")}return h}var r=s(e,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,h;var o=r.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=void 0),n.delegate=null,h):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function x(t){var n={tryLoc:t[0]};1 in t&&(n.catchLoc=t[1]),2 in t&&(n.finallyLoc=t[2],n.afterLoc=t[3]),this.tryEntries.push(n)}function M(t){var n=t.completion||{};n.type="normal",delete n.arg,t.completion=n}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(x,this),this.reset(!0)}function k(t){if(t){var n=t[u];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,o=function n(){for(;++r<t.length;)if(e.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=void 0,n.done=!0,n};return o.next=o}}return{next:N}}function N(){return{value:void 0,done:!0}}return d.prototype=v,l(w,"constructor",v),l(v,"constructor",d),d.displayName=l(v,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var n="function"==typeof t&&t.constructor;return!!n&&(n===d||"GeneratorFunction"===(n.displayName||n.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,c,"GeneratorFunction")),t.prototype=Object.create(w),t},t.awrap=function(t){return{__await:t}},_(b.prototype),l(b.prototype,a,(function(){return this})),t.AsyncIterator=b,t.async=function(n,e,r,o,i){void 0===i&&(i=Promise);var u=new b(f(n,e,r,o),i);return t.isGeneratorFunction(e)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},_(w),l(w,c,"Generator"),l(w,u,(function(){return this})),l(w,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var n=[];for(var e in t)n.push(e);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=k,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(M),!t)for(var n in this)"t"===n.charAt(0)&&e.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(e,r){return u.type="throw",u.arg=t,n.next=e,r&&(n.method="next",n.arg=void 0),!!r}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var a=e.call(i,"catchLoc"),c=e.call(i,"finallyLoc");if(a&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(t,n){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&e.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=n&&n<=i.finallyLoc&&(i=null);var u=i?i.completion:{};return u.type=t,u.arg=n,i?(this.method="next",this.next=i.finallyLoc,h):this.complete(u)},complete:function(t,n){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&n&&(this.next=n),h},finish:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),M(e),h}},catch:function(t){for(var n=this.tryEntries.length-1;n>=0;--n){var e=this.tryEntries[n];if(e.tryLoc===t){var r=e.completion;if("throw"===r.type){var o=r.arg;M(e)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,e){return this.delegate={iterator:k(t),resultName:n,nextLoc:e},"next"===this.method&&(this.arg=void 0),h}},t}}}]);
//# sourceMappingURL=9025.9adb0031.chunk.js.map