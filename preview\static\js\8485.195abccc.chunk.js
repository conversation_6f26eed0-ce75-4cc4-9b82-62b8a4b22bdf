"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[8485],{28485:function(e,a,s){s.r(a),s.d(a,{Calendar:function(){return x}});var c=s(15671),l=s(43144),t=s(60136),n=s(54062),r=s(72791),i=s(20502),d=s(121),h=s(53824),m=s(9309),o=s(80184),x=function(e){(0,t.Z)(s,e);var a=(0,n.Z)(s);function s(){var e;return(0,c.Z)(this,s),(e=a.call(this)).calendarComponentRef=r.createRef(),e.handleDateClick=function(a){window.confirm("Would you like to add an event to "+a.dateStr+" ?")&&e.setState({calendarEvents:e.state.calendarEvents.concat({title:"New Event",start:a.date,allDay:a.allDay})})},e.state={calendarWeekends:!0,calendarEvents:[{title:"Event Now",start:new Date}]},e}return(0,l.Z)(s,[{key:"render",value:function(){return(0,o.jsx)("div",{children:(0,o.jsxs)("div",{className:"row",children:[(0,o.jsxs)("div",{className:"col-md-3",children:[(0,o.jsxs)("div",{className:"fc-external-events",children:[(0,o.jsxs)("div",{className:"fc-event",children:[(0,o.jsx)("p",{children:"Deciphering Marketing Lingo For Small Business Owners"}),(0,o.jsx)("p",{className:"small-text"}),(0,o.jsx)("p",{className:"text-muted mb-0",children:"Georgia"})]}),(0,o.jsxs)("div",{className:"fc-event",children:[(0,o.jsx)("p",{children:"Influencing The Influencer"}),(0,o.jsx)("p",{className:"small-text"}),(0,o.jsx)("p",{className:"text-muted mb-0",children:"Netherlands"})]}),(0,o.jsxs)("div",{className:"fc-event",children:[(0,o.jsx)("p",{children:"Profiles Of The Powerful Advertising Exec Steve Grasse"}),(0,o.jsx)("p",{className:"small-text"}),(0,o.jsx)("p",{className:"text-muted mb-0",children:"Canada"})]})]}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("p",{children:"Filter board"}),(0,o.jsx)("div",{className:"form-check form-check-primary",children:(0,o.jsxs)("label",{className:"form-check-label",children:[(0,o.jsx)("input",{type:"checkbox",className:"form-check-input",defaultChecked:!0}),(0,o.jsx)("i",{className:"input-helper"}),"Project Board"]})}),(0,o.jsx)("div",{className:"form-check form-check-danger",children:(0,o.jsxs)("label",{className:"form-check-label",children:[(0,o.jsx)("input",{type:"checkbox",className:"form-check-input",defaultChecked:!0}),(0,o.jsx)("i",{className:"input-helper"}),"Kanban Board"]})}),(0,o.jsx)("div",{className:"form-check form-check-info",children:(0,o.jsxs)("label",{className:"form-check-label",children:[(0,o.jsx)("input",{type:"checkbox",className:"form-check-input",defaultChecked:!0}),(0,o.jsx)("i",{className:"input-helper"}),"Summary Board"]})}),(0,o.jsx)("div",{className:"form-check form-check-success",children:(0,o.jsxs)("label",{className:"form-check-label",children:[(0,o.jsx)("input",{type:"checkbox",className:"form-check-input",defaultChecked:!0}),(0,o.jsx)("i",{className:"input-helper"}),"Planner Board"]})})]})]}),(0,o.jsx)("div",{className:"col-md-9",children:(0,o.jsx)("div",{className:"card",children:(0,o.jsxs)("div",{className:"card-body",children:[(0,o.jsx)("h4",{className:"card-title",children:"Fullcalendar"}),(0,o.jsx)(i.Z,{defaultView:"dayGridMonth",header:{left:"prev,next today",center:"title",right:"dayGridMonth,timeGridWeek,timeGridDay,listWeek"},plugins:[d.ZP,h.ZP,m.ZP],ref:this.calendarComponentRef,weekends:this.state.calendarWeekends,events:this.state.calendarEvents,dateClick:this.handleDateClick})]})})})]})})}}]),s}(r.Component);a.default=x}}]);
//# sourceMappingURL=8485.195abccc.chunk.js.map